# دليل نظام التفويض المبسط

## نظرة عامة

تم إنشاء نظام تفويض مبسط يعمل بدون مكتبات خارجية لحل مشكلة `jsonwebtoken` المفقودة. هذا النظام مناسب للتطوير والاختبار.

## كيفية الاستخدام

### 1. إنشاء التوكنات

```javascript
import { createSimpleToken } from '@/lib/auth';

// إنشاء توكن للمدير
const adminToken = createSimpleToken('admin', 'admin');
console.log('Admin Token:', adminToken);

// إنشاء توكن للمدير المساعد
const managerToken = createSimpleToken('manager', 'manager');
console.log('Manager Token:', managerToken);

// إنشاء توكن للمستخدم العادي
const userToken = createSimpleToken('user', 'user');
console.log('User Token:', userToken);
```

### 2. استخدام التوكنات في الطلبات

```javascript
// في fetch requests
fetch('/api/clients', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  }
});
```

### 3. التوكنات الجاهزة للاختبار

```bash
# Admin Token (username: admin, role: admin)
Authorization: Bearer dXNlcjphZG1pbjphZG1pbg==

# Manager Token (username: manager, role: manager)  
Authorization: Bearer dXNlcjptYW5hZ2VyOm1hbmFnZXI=

# User Token (username: user, role: user)
Authorization: Bearer dXNlcjp1c2VyOnVzZXI=

# Guest Token (username: guest, role: guest)
Authorization: Bearer dXNlcjpndWVzdDpndWVzdA==
```

## مستويات الصلاحيات

1. **admin** - صلاحيات كاملة (جميع العمليات)
2. **manager** - صلاحيات إدارية (حذف، تعديل، عرض، إنشاء)
3. **user** - صلاحيات أساسية (تعديل، عرض، إنشاء)
4. **guest** - صلاحيات عرض فقط

## اختبار النظام

### باستخدام Postman أو أي HTTP client:

```http
GET /api/clients
Authorization: Bearer dXNlcjphZG1pbjphZG1pbg==
```

### باستخدام curl:

```bash
curl -H "Authorization: Bearer dXNlcjphZG1pbjphZG1pbg==" \
     http://localhost:3000/api/clients
```

### باستخدام JavaScript:

```javascript
// اختبار مع توكن صحيح
const response = await fetch('/api/clients', {
  headers: {
    'Authorization': 'Bearer dXNlcjphZG1pbjphZG1pbg=='
  }
});

// اختبار بدون توكن (يجب أن يفشل)
const response2 = await fetch('/api/clients');
```

## رسائل الخطأ المتوقعة

```json
// بدون Authorization header
{
  "error": "Missing or invalid authorization header"
}

// توكن غير صحيح
{
  "error": "Invalid or expired token"
}

// صلاحيات غير كافية
{
  "error": "Insufficient permissions"
}
```

## للإنتاج

⚠️ **تحذير:** هذا النظام مبسط وغير آمن للإنتاج!

للإنتاج، يُنصح بـ:

1. **تثبيت JWT:**
   ```bash
   npm install jsonwebtoken @types/jsonwebtoken
   ```

2. **استخدام JWT مع secret قوي:**
   ```env
   JWT_SECRET=your-super-secret-key-at-least-32-characters-long
   ```

3. **إضافة انتهاء صلاحية للتوكنات**
4. **إضافة refresh tokens**
5. **استخدام HTTPS في الإنتاج**

## استكشاف الأخطاء

### المشكلة: "Module not found: Can't resolve 'jsonwebtoken'"
**الحل:** تم حلها بالنظام المبسط الحالي

### المشكلة: "Invalid or expired token"
**الحل:** تأكد من استخدام التوكنات الصحيحة المذكورة أعلاه

### المشكلة: "Insufficient permissions"
**الحل:** استخدم توكن بصلاحيات أعلى (admin أو manager)

## أمثلة عملية

```javascript
// إنشاء مستخدم جديد (يتطلب صلاحيات user أو أعلى)
const createUser = async () => {
  const response = await fetch('/api/users', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer dXNlcjp1c2VyOnVzZXI=',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      name: 'مستخدم جديد',
      email: '<EMAIL>'
    })
  });
  
  return response.json();
};

// حذف مستخدم (يتطلب صلاحيات manager أو أعلى)
const deleteUser = async (userId) => {
  const response = await fetch('/api/users', {
    method: 'DELETE',
    headers: {
      'Authorization': 'Bearer dXNlcjptYW5hZ2VyOm1hbmFnZXI=',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ id: userId })
  });
  
  return response.json();
};
```

النظام الآن جاهز للاستخدام! 🎉
