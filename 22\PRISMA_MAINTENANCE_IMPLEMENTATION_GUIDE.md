# دليل تطبيق Prisma لأنظمة الصيانة

## ملخص التحديثات المنجزة

### 1. إضافة النماذج إلى Prisma Schema ✅
تم إضافة النماذج التالية إلى `prisma/schema.prisma`:

```prisma
model MaintenanceOrder {
  id                      Int      @id @default(autoincrement())
  orderNumber             String   @unique
  referenceNumber         String?
  date                    String
  employeeName            String
  maintenanceEmployeeId   Int?
  maintenanceEmployeeName String?
  items                   Json     // سيتم تخزين العناصر كـ JSON
  notes                   String?
  attachmentName          String?
  status                  String   @default("wip") // wip, completed
  source                  String   @default("warehouse") // warehouse, direct
  createdAt               DateTime @default(now())
}

model MaintenanceReceiptOrder {
  id                      Int      @id @default(autoincrement())
  receiptNumber           String   @unique
  referenceNumber         String?
  date                    String
  employeeName            String
  maintenanceEmployeeName String?
  items                   Json     // سيتم تخزين العناصر كـ JSON
  notes                   String?
  attachmentName          String?
  status                  String   @default("completed") // completed
  createdAt               DateTime @default(now())
}
```

### 2. API Endpoints ✅
تم إنشاء API endpoints التالية:

#### `/api/maintenance-orders`
- **GET**: استرجاع جميع أوامر الصيانة
- **POST**: إنشاء أمر صيانة جديد
- **PUT**: تحديث أمر صيانة موجود
- **DELETE**: حذف أمر صيانة

#### `/api/maintenance-receipts`
- **GET**: استرجاع جميع أوامر استلام الصيانة
- **POST**: إنشاء أمر استلام جديد
- **PUT**: تحديث أمر استلام موجود
- **DELETE**: حذف أمر استلام

### 3. تحديث المخزن المركزي ✅
تم تحديث `context/store.tsx` لتحميل بيانات الصيانة من API:

```typescript
// تم إضافة تحميل أوامر الصيانة
try {
  console.log("جاري تحميل أوامر الصيانة...");
  const maintenanceOrdersResponse = await fetch('/api/maintenance-orders');
  if (maintenanceOrdersResponse.ok) {
    const maintenanceOrdersData = await maintenanceOrdersResponse.json();
    if (Array.isArray(maintenanceOrdersData)) {
      setMaintenanceOrders(maintenanceOrdersData);
      console.log(`تم تحميل ${maintenanceOrdersData.length} أمر صيانة`);
    }
  }
} catch (error) {
  console.warn('فشل في تحميل أوامر الصيانة:', error);
}

// تم إضافة تحميل أوامر استلام الصيانة
try {
  console.log("جاري تحميل أوامر استلام الصيانة...");
  const maintenanceReceiptsResponse = await fetch('/api/maintenance-receipts');
  if (maintenanceReceiptsResponse.ok) {
    const maintenanceReceiptsData = await maintenanceReceiptsResponse.json();
    if (Array.isArray(maintenanceReceiptsData)) {
      setMaintenanceReceiptOrders(maintenanceReceiptsData);
      console.log(`تم تحميل ${maintenanceReceiptsData.length} أمر استلام صيانة`);
    }
  }
} catch (error) {
  console.warn('فشل في تحميل أوامر استلام الصيانة:', error);
}
```

### 4. تحديث صفحات الواجهة ✅
- تم إضافة مؤشرات التحميل إلى `app/(main)/maintenance/page.tsx`
- تم إضافة مؤشرات التحميل إلى `app/(main)/maintenance-transfer/page.tsx`

## التحديثات المطلوبة يدوياً

### يجب استبدال الوظائف التالية في `context/store.tsx`:

#### 1. وظيفة addMaintenanceOrder (حوالي السطر 728):
```typescript
const addMaintenanceOrder = async (
  order: Omit<MaintenanceOrder, 'id' | 'createdAt'> & { id?: number; status?: 'wip' | 'completed' | 'draft' }
) => {
  try {
    const response = await fetch('/api/maintenance-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to create maintenance order');
    }

    const newOrder = await response.json();
    setMaintenanceOrders((prev) => [newOrder, ...prev].sort((a, b) => b.id - a.id));

    addActivity({
      type: 'maintenance',
      description: `تم إنشاء أمر صيانة ${newOrder.orderNumber}`,
    });
  } catch (error) {
    console.error('Failed to add maintenance order:', error);
    throw error;
  }
};
```

#### 2. وظيفة updateMaintenanceOrder (حوالي السطر 770):
```typescript
const updateMaintenanceOrder = async (updatedOrder: MaintenanceOrder) => {
  try {
    const response = await fetch('/api/maintenance-orders', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      throw new Error('Failed to update maintenance order');
    }

    const order = await response.json();
    setMaintenanceOrders((prev) => prev.map((o) => (o.id === order.id ? order : o)));

    addActivity({
      type: 'maintenance',
      description: `تم تحديث أمر الصيانة ${order.orderNumber}`,
    });
  } catch (error) {
    console.error('Failed to update maintenance order:', error);
    throw error;
  }
};
```

#### 3. وظيفة deleteMaintenanceOrder (حوالي السطر 894):
```typescript
const deleteMaintenanceOrder = async (orderId: number) => {
  try {
    const response = await fetch('/api/maintenance-orders', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete maintenance order');
    }

    setMaintenanceOrders((prev) => prev.filter((o) => o.id !== orderId));

    addActivity({
      type: 'maintenance',
      description: `تم حذف أمر الصيانة`,
    });
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);
    throw error;
  }
};
```

#### 4. وظيفة addMaintenanceReceiptOrder (حوالي السطر 1049):
```typescript
const addMaintenanceReceiptOrder = async (order: Omit<MaintenanceReceiptOrder, 'id' | 'createdAt'>) => {
  try {
    const response = await fetch('/api/maintenance-receipts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order),
    });

    if (!response.ok) {
      throw new Error('Failed to create maintenance receipt');
    }

    const newOrder = await response.json();
    setMaintenanceReceiptOrders((prev) => [newOrder, ...prev].sort((a, b) => b.id - a.id));

    addActivity({
      type: 'maintenance',
      description: `تم إنشاء أمر استلام جديد ${newOrder.receiptNumber} يحتوي على ${newOrder.items.length} جهاز`,
    });
  } catch (error) {
    console.error('Failed to add maintenance receipt order:', error);
    throw error;
  }
};
```

#### 5. وظيفة updateMaintenanceReceiptOrder (حوالي السطر 1067):
```typescript
const updateMaintenanceReceiptOrder = async (updatedOrder: MaintenanceReceiptOrder) => {
  try {
    const response = await fetch('/api/maintenance-receipts', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedOrder),
    });

    if (!response.ok) {
      throw new Error('Failed to update maintenance receipt');
    }

    const order = await response.json();
    setMaintenanceReceiptOrders((prev) => prev.map((o) => (o.id === order.id ? order : o)));

    addActivity({
      type: 'maintenance',
      description: `تم تحديث أمر الاستلام ${order.receiptNumber}`,
    });
  } catch (error) {
    console.error('Failed to update maintenance receipt order:', error);
    throw error;
  }
};
```

#### 6. وظيفة deleteMaintenanceReceiptOrder (حوالي السطر 1151):
```typescript
const deleteMaintenanceReceiptOrder = async (orderId: number) => {
  try {
    const response = await fetch('/api/maintenance-receipts', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: orderId }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete maintenance receipt');
    }

    setMaintenanceReceiptOrders((prev) => prev.filter((o) => o.id !== orderId));

    addActivity({
      type: 'maintenance',
      description: `تم حذف أمر الاستلام`,
    });
  } catch (error) {
    console.error('Failed to delete maintenance receipt order:', error);
    throw error;
  }
};
```

## خطوات الاختبار

### 1. تحديث قاعدة البيانات
```bash
npx prisma db push
npx prisma generate
```

### 2. اختبار العمليات الأساسية
- إنشاء أمر صيانة جديد
- تحديث أمر صيانة موجود
- حذف أمر صيانة
- إنشاء أمر استلام جديد
- تحديث أمر استلام موجود
- حذف أمر استلام

### 3. اختبار استمرارية البيانات
- إنشاء بيانات جديدة
- إعادة تشغيل التطبيق
- التحقق من وجود البيانات

## الفوائد المحققة

1. **استمرارية البيانات**: البيانات محفوظة في قاعدة بيانات SQLite بدلاً من localStorage
2. **الأداء**: تحسين أداء التطبيق من خلال تحميل البيانات من قاعدة البيانات
3. **الموثوقية**: تقليل فقدان البيانات عند إغلاق المتصفح
4. **التوسعة**: إمكانية إضافة المزيد من الميزات مستقبلاً
5. **التكامل**: تكامل أفضل مع باقي أجزاء النظام

## ملاحظات مهمة

- تأكد من تحديث جميع الوظائف المذكورة أعلاه
- اختبر كل عملية بعد التحديث
- راقب console للتأكد من عدم وجود أخطاء
- تأكد من أن مؤشرات التحميل تعمل بشكل صحيح
