const fs = require('fs');
const path = require('path');

const filePath = 'context/store.tsx';

// قراءة الملف
let content = fs.readFileSync(filePath, 'utf8');

// إضافة import للـ api-client في بداية الملف
if (!content.includes('@/lib/api-client')) {
  const importIndex = content.indexOf('import type { ActivityLog }');
  if (importIndex !== -1) {
    const beforeImport = content.substring(0, importIndex);
    const afterImport = content.substring(importIndex);
    content = beforeImport + 'import { apiClient, handleApiResponse } from "@/lib/api-client";\n' + afterImport;
  }
}

// إصلاح loadDataFromAPIs function
const loadDataStart = 'const loadDataFromAPIs = async () => {';
const loadDataStartIndex = content.indexOf(loadDataStart);

if (loadDataStartIndex !== -1) {
  // البحث عن نهاية الوظيفة
  let bracketCount = 0;
  let searchIndex = loadDataStartIndex + loadDataStart.length;
  let endIndex = -1;
  
  for (let i = searchIndex; i < content.length; i++) {
    if (content[i] === '{') {
      bracketCount++;
    } else if (content[i] === '}') {
      bracketCount--;
      if (bracketCount === 0) {
        endIndex = i + 1;
        break;
      }
    }
  }

  if (endIndex !== -1) {
    const newLoadDataFunction = `const loadDataFromAPIs = async () => {
    try {
      setIsLoading(true);
      console.log("بدء تحميل بيانات التطبيق من API...");

      // Load users
      try {
        console.log("جاري تحميل بيانات المستخدمين...");
        const usersResponse = await apiClient.get("/api/users");
        const usersData = await handleApiResponse(usersResponse);
        console.log("تم تحميل المستخدمين:", usersData);
        if (Array.isArray(usersData) && usersData.length > 0) {
          setUsers(usersData);

          // إنشاء صلاحيات كاملة للمستخدم الأول إذا لم تكن موجودة
          const firstUser = usersData[0];
          if (!firstUser.permissions) {
            const permissions = {} as AppPermissions;
            permissionPages.forEach((page) => {
              permissions[page] = {
                view: true,
                create: true,
                edit: true,
                delete: true,
                viewAll: true,
                manage: [1, 2, 3],
                acceptWithoutWarranty: true,
              };
            });
            firstUser.permissions = permissions;
          }

          setCurrentUser(firstUser);
          console.log("تم تعيين المستخدم الحالي:", firstUser);
        } else {
          console.warn("لم يتم العثور على مستخدمين");

          // إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
          const defaultUser = {
            id: 1,
            name: "مدير النظام",
            username: "admin",
            email: "<EMAIL>",
            permissions: {} as AppPermissions,
          };

          // إضافة صلاحيات كاملة للمستخدم الافتراضي
          permissionPages.forEach((page) => {
            defaultUser.permissions[page] = {
              view: true,
              create: true,
              edit: true,
              delete: true,
              viewAll: true,
              manage: [1, 2, 3],
              acceptWithoutWarranty: true,
            };
          });

          setUsers([defaultUser]);
          setCurrentUser(defaultUser);
          console.log("تم إنشاء مستخدم افتراضي:", defaultUser);
        }
      } catch (error) {
        console.error("خطأ في تحميل المستخدمين:", error);
        setUsers(initialUsers);
        setCurrentUser(initialUsers[0]);
      }

      // Load other data with auth headers...
      try {
        const [
          devicesRes,
          warehousesRes,
          supplyRes,
          salesRes,
          returnsRes,
          clientsRes,
          suppliersRes,
          deliveryOrdersRes
        ] = await Promise.all([
          apiClient.get("/api/devices"),
          apiClient.get("/api/warehouses"),
          apiClient.get("/api/supply"),
          apiClient.get("/api/sales"),
          apiClient.get("/api/returns"),
          apiClient.get("/api/clients"),
          apiClient.get("/api/suppliers"),
          apiClient.get("/api/delivery-orders")
        ]);

        const [
          devicesData,
          warehousesData,
          supplyData,
          salesData,
          returnsData,
          clientsData,
          suppliersData,
          deliveryOrdersData
        ] = await Promise.all([
          handleApiResponse(devicesRes),
          handleApiResponse(warehousesRes),
          handleApiResponse(supplyRes),
          handleApiResponse(salesRes),
          handleApiResponse(returnsRes),
          handleApiResponse(clientsRes),
          handleApiResponse(suppliersRes),
          handleApiResponse(deliveryOrdersRes)
        ]);

        // Set data
        if (Array.isArray(devicesData)) setDevices(devicesData);
        if (Array.isArray(warehousesData)) setWarehouses(warehousesData);
        if (Array.isArray(supplyData)) setSupplyOrders(supplyData);
        if (Array.isArray(salesData)) setSales(salesData);
        if (Array.isArray(returnsData)) setReturns(returnsData);
        if (Array.isArray(clientsData)) setClients(clientsData);
        if (Array.isArray(suppliersData)) setSuppliers(suppliersData);
        if (Array.isArray(deliveryOrdersData)) setDeliveryOrders(deliveryOrdersData);

        console.log("تم تحميل جميع البيانات بنجاح");
      } catch (error) {
        console.error("خطأ في تحميل البيانات:", error);
        // Use initial data as fallback
      }

    } catch (error) {
      console.error("خطأ عام في تحميل البيانات:", error);
    } finally {
      setIsLoading(false);
    }
  };`;

    const beforeFunction = content.substring(0, loadDataStartIndex);
    const afterFunction = content.substring(endIndex);
    content = beforeFunction + newLoadDataFunction + afterFunction;
  }
}

// كتابة الملف المُحدث
fs.writeFileSync(filePath, content, 'utf8');

console.log('تم إصلاح store.tsx وإضافة headers التفويض!');
console.log('الآن جميع طلبات API ستتضمن headers التفويض المطلوبة.');
