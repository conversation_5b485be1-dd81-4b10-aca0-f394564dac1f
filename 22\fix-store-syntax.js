const fs = require('fs');
const path = require('path');

console.log('بدء إصلاح ملف store.tsx...');

const filePath = 'context/store.tsx';

try {
  // قراءة الملف
  let content = fs.readFileSync(filePath, 'utf8');
  
  // إزالة أي أسطر فارغة أو أحرف غريبة قد تسبب مشاكل
  content = content.replace(/\r\n/g, '\n'); // توحيد line endings
  
  // التحقق من وجود الإغلاق الصحيح للـ value object
  const valueStart = content.indexOf('const value = {');
  const importStoreDataIndex = content.indexOf('importStoreData,', valueStart);
  
  if (valueStart !== -1 && importStoreDataIndex !== -1) {
    // البحث عن القوس المناسب بعد importStoreData
    let searchIndex = importStoreDataIndex + 'importStoreData,'.length;
    let foundClosingBrace = false;
    
    // البحث عن الإغلاق الصحيح
    for (let i = searchIndex; i < content.length; i++) {
      if (content.substring(i, i + 4) === '  };') {
        foundClosingBrace = true;
        break;
      }
    }
    
    if (!foundClosingBrace) {
      console.log('إصلاح مشكلة إغلاق value object...');
      
      // البحث عن نهاية importStoreData line
      const endOfImportLine = content.indexOf('\n', importStoreDataIndex);
      if (endOfImportLine !== -1) {
        const beforeImport = content.substring(0, endOfImportLine);
        const afterImport = content.substring(endOfImportLine);
        
        content = beforeImport + '\n  };\n' + afterImport;
      }
    }
  }
  
  // التأكد من وجود الـ exports في النهاية
  if (!content.includes('export function useStore()')) {
    console.error('Missing useStore export!');
    process.exit(1);
  }
  
  if (!content.includes('export function StoreProvider')) {
    console.error('Missing StoreProvider export!');
    process.exit(1);
  }
  
  // كتابة الملف المُصحح
  fs.writeFileSync(filePath, content, 'utf8');
  
  console.log('✅ تم إصلاح store.tsx بنجاح!');
  console.log('الآن يجب أن تعمل الـ exports بشكل صحيح.');
  
} catch (error) {
  console.error('خطأ في إصلاح الملف:', error);
  process.exit(1);
}
