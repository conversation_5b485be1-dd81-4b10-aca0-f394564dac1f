const fs = require('fs');

console.log('🔧 إصلاح خطأ البناء في store.tsx...');

try {
  let content = fs.readFileSync('context/store.tsx', 'utf8');
  const lines = content.split('\n');
  
  console.log('العدد الكلي للأسطر:', lines.length);
  
  // البحث عن السطر المشكلة حول السطر 3535
  for (let i = 3530; i < Math.min(3540, lines.length); i++) {
    console.log(`السطر ${i + 1}: "${lines[i]}"`);
  }
  
  // البحث عن السطر الذي يحتوي فقط على }
  let problematicLineIndex = -1;
  for (let i = 3530; i < 3540 && i < lines.length; i++) {
    if (lines[i].trim() === '}' && i > 3533) {
      problematicLineIndex = i;
      break;
    }
  }
  
  if (problematicLineIndex !== -1) {
    console.log(`تم العثور على قوس زائد في السطر ${problematicLineIndex + 1}`);
    
    // حذف السطر المشكلة
    lines.splice(problematicLineIndex, 1);
    
    // إعادة كتابة الملف
    const newContent = lines.join('\n');
    fs.writeFileSync('context/store.tsx', newContent, 'utf8');
    
    console.log('✅ تم حذف القوس الزائد بنجاح!');
  } else {
    console.log('❌ لم يتم العثور على قوس زائد');
    
    // طباعة الأسطر المحيطة بالخطأ للفهم بشكل أفضل
    console.log('\nالأسطر المحيطة بالخطأ:');
    for (let i = 3530; i < 3540 && i < lines.length; i++) {
      console.log(`${i + 1}: ${lines[i]}`);
    }
  }
  
} catch (error) {
  console.error('❌ خطأ:', error.message);
}
