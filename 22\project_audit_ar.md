# تقرير تدقيق شامل للمشروع

تاريخ الإنشاء: 2025-07-24 03:25

هذا التقرير يتضمن تحليلًا آليًا لجميع ملفات **route.ts** داخل مجلد `app/api` بالإضافة إلى ملف **schema.prisma** إن وُجد، مع شرح عربي لكل مشكلة محتملة.

---

## ملف: `app/api/attachments/delete/route.ts`

### (1) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 8

```typescript
const ATTACHMENTS_DIR = path.join(process.cwd(), 'public', 'attachments');

export async function DELETE(request: NextRequest) {
  try {
    const { fileName } = await request.json();
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (2) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 8

```typescript
const ATTACHMENTS_DIR = path.join(process.cwd(), 'public', 'attachments');

export async function DELETE(request: NextRequest) {
  try {
    const { fileName } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

## ملف: `app/api/attachments/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 16

```typescript
}

export async function POST(request: NextRequest) {
  try {
    await ensureAttachmentsDir();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

## ملف: `app/api/audit-logs/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 10

```typescript
}

export async function POST(request: Request) {
  const { userId, username, operation, details }: AuditLog = await request.json();
  const newLog = await prisma.auditLog.create({
    data: {
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 12

```typescript
export async function POST(request: Request) {
  const { userId, username, operation, details }: AuditLog = await request.json();
  const newLog = await prisma.auditLog.create({
    data: {
      userId,
      username,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/clients/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 17

```typescript
}

export async function POST(request: Request) {
  try {
    const newClient = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 42

```typescript
    
    // إنشاء العميل في قاعدة البيانات
    const client = await prisma.client.create({
      data: {
        name: newClient.name,
        phone: newClient.phone || '',
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 52

```typescript
    return NextResponse.json(client, { status: 201 });
  } catch (error) {
    console.error('Failed to create client:', error);
    return NextResponse.json({ error: 'Failed to create client' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 53

```typescript
  } catch (error) {
    console.error('Failed to create client:', error);
    return NextResponse.json({ error: 'Failed to create client' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 57

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedClient = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 85

```typescript
    
    // تحديث العميل
    const client = await prisma.client.update({
      where: { id: updatedClient.id },
      data: {
        name: updatedClient.name,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 96

```typescript
    return NextResponse.json(client);
  } catch (error) {
    console.error('Failed to update client:', error);
    return NextResponse.json({ error: 'Failed to update client' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 97

```typescript
  } catch (error) {
    console.error('Failed to update client:', error);
    return NextResponse.json({ error: 'Failed to update client' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 101

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 101

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 135

```typescript
    if (relatedOperations.length > 0) {
      return NextResponse.json({
        error: 'Cannot delete client',
        reason: 'يوجد عمليات مرتبطة بهذا العميل',
        relatedOperations
      }, { status: 409 });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 142

```typescript
    
    // حذف العميل
    await prisma.client.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (13) استدعاء prisma.delete — السطر 142

```typescript
    
    // حذف العميل
    await prisma.client.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 148

```typescript
    return NextResponse.json({ message: 'Client deleted successfully' });
  } catch (error) {
    console.error('Failed to delete client:', error);
    return NextResponse.json({ error: 'Failed to delete client' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 149

```typescript
  } catch (error) {
    console.error('Failed to delete client:', error);
    return NextResponse.json({ error: 'Failed to delete client' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/database/backup/route.ts`

### (1) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 18

```typescript
) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        username,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (2) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 62

```typescript
}

export async function POST(request: Request) {
  try {
    const { connectionId, name, description, createdBy } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 97

```typescript

    // حفظ معلومات النسخة الاحتياطية في قاعدة البيانات أولاً
    const backup = await prisma.databaseBackup.create({
      data: {
        name: name || `نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`,
        description: description || '',
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 143

```typescript

      // تحديث معلومات النسخة الاحتياطية
      const updatedBackup = await prisma.databaseBackup.update({
        where: { id: backup.id },
        data: {
          fileSize,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 169

```typescript
    } catch (backupError) {
      // تحديث حالة النسخة الاحتياطية إلى فاشلة
      await prisma.databaseBackup.update({
        where: { id: backup.id },
        data: {
          status: 'failed'
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 178

```typescript
      console.error('Backup command error:', backupError);
      return NextResponse.json(
        { error: 'Failed to create database backup: ' + (backupError as Error).message },
        { status: 500 }
      );
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 185

```typescript
    console.error('Backup creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create database backup' },
      { status: 500 }
    );
  }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 191

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 191

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 225

```typescript

    // حذف النسخة الاحتياطية من قاعدة البيانات
    await prisma.databaseBackup.delete({
      where: { id }
    });

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) استدعاء prisma.delete — السطر 225

```typescript

    // حذف النسخة الاحتياطية من قاعدة البيانات
    await prisma.databaseBackup.delete({
      where: { id }
    });

```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (12) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 240

```typescript
    });
  } catch (error) {
    console.error('Failed to delete backup:', error);
    return NextResponse.json(
      { error: 'Failed to delete database backup' },
      { status: 500 }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 242

```typescript
    console.error('Failed to delete backup:', error);
    return NextResponse.json(
      { error: 'Failed to delete database backup' },
      { status: 500 }
    );
  }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/database/connections/route.ts`

### (1) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 12

```typescript
) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        username,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (2) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 54

```typescript
}

export async function POST(request: Request) {
  try {
    const data = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 71

```typescript
    // إذا كان هذا الاتصال الافتراضي، قم بإلغاء الافتراضي للآخرين
    if (data.isDefault) {
      await prisma.databaseConnection.updateMany({
        where: { isDefault: true },
        data: { isDefault: false }
      });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 77

```typescript
    }

    const connection = await prisma.databaseConnection.create({
      data: {
        name: data.name,
        host: data.host,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 102

```typescript
    }, { status: 201 });
  } catch (error) {
    console.error('Failed to create connection:', error);
    return NextResponse.json(
      { error: 'Failed to create database connection' },
      { status: 500 }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 104

```typescript
    console.error('Failed to create connection:', error);
    return NextResponse.json(
      { error: 'Failed to create database connection' },
      { status: 500 }
    );
  }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 110

```typescript
}

export async function PUT(request: Request) {
  try {
    const data = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 135

```typescript
    // إذا كان هذا الاتصال الافتراضي، قم بإلغاء الافتراضي للآخرين
    if (data.isDefault && !existingConnection.isDefault) {
      await prisma.databaseConnection.updateMany({
        where: { isDefault: true },
        data: { isDefault: false }
      });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 157

```typescript
    }

    const connection = await prisma.databaseConnection.update({
      where: { id: data.id },
      data: updateData
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 174

```typescript
    });
  } catch (error) {
    console.error('Failed to update connection:', error);
    return NextResponse.json(
      { error: 'Failed to update database connection' },
      { status: 500 }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 176

```typescript
    console.error('Failed to update connection:', error);
    return NextResponse.json(
      { error: 'Failed to update database connection' },
      { status: 500 }
    );
  }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 182

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (13) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 182

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 206

```typescript

    // حذف الاتصال (سيحذف النسخ الاحتياطية المرتبطة تلقائياً بسبب onDelete: Cascade)
    await prisma.databaseConnection.delete({
      where: { id }
    });

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) استدعاء prisma.delete — السطر 206

```typescript

    // حذف الاتصال (سيحذف النسخ الاحتياطية المرتبطة تلقائياً بسبب onDelete: Cascade)
    await prisma.databaseConnection.delete({
      where: { id }
    });

```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 221

```typescript
    });
  } catch (error) {
    console.error('Failed to delete connection:', error);
    return NextResponse.json(
      { error: 'Failed to delete database connection' },
      { status: 500 }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 223

```typescript
    console.error('Failed to delete connection:', error);
    return NextResponse.json(
      { error: 'Failed to delete database connection' },
      { status: 500 }
    );
  }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/database/create/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 9

```typescript
const execAsync = promisify(exec);

export async function POST(request: Request) {
  try {
    const { connectionId, name, owner, template, encoding } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 41

```typescript
    }, { status: 201 });
  } catch (error) {
    console.error('Create database error:', error);
    
    // التحقق من نوع الخطأ لإعطاء رسالة واضحة
    if (error instanceof Error) {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 53

```typescript
      if (error.message.includes('permission denied')) {
        return NextResponse.json(
          { error: 'Permission denied to create database' },
          { status: 403 }
        );
      }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 60

```typescript

    return NextResponse.json(
      { error: 'Failed to create database' },
      { status: 500 }
    );
  }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/database/delete/route.ts`

### (1) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 8

```typescript
const execAsync = promisify(exec);

export async function DELETE(request: Request) {
  try {
    const { connectionId, databaseName } = await request.json();
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (2) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 8

```typescript
const execAsync = promisify(exec);

export async function DELETE(request: Request) {
  try {
    const { connectionId, databaseName } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 28

```typescript
    if (protectedDatabases.includes(databaseName.toLowerCase())) {
      return NextResponse.json(
        { error: 'Cannot delete system database' },
        { status: 403 }
      );
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 49

```typescript
    });
  } catch (error) {
    console.error('Delete database error:', error);
    
    // التحقق من نوع الخطأ لإعطاء رسالة واضحة
    if (error instanceof Error) {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 67

```typescript
      if (error.message.includes('permission denied')) {
        return NextResponse.json(
          { error: 'Permission denied to delete database' },
          { status: 403 }
        );
      }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 74

```typescript

    return NextResponse.json(
      { error: 'Failed to delete database' },
      { status: 500 }
    );
  }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/database/restore/route.ts`

### (1) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 16

```typescript
) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        username,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (2) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 29

```typescript
}

export async function POST(request: Request) {
  try {
    const { backupId, targetConnectionId, restoredBy } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

## ملف: `app/api/database/switch/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 4

```typescript
import { prisma } from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    const { connectionId, databaseName } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 21

```typescript

    // تحديث اتصال قاعدة البيانات للاتصال الجديد
    await prisma.databaseConnection.update({
      where: { id: connectionId },
      data: { 
        database: databaseName,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/delivery-orders/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 25

```typescript
}

export async function POST(request: Request) {
  try {
    const newOrder = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 45

```typescript
    const deliveryOrderNumber = `DEL-${newId}`;

    // Create the delivery order in the database
    const order = await prisma.deliveryOrder.create({
      data: {
        deliveryOrderNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 46

```typescript

    // Create the delivery order in the database
    const order = await prisma.deliveryOrder.create({
      data: {
        deliveryOrderNumber,
        referenceNumber: newOrder.referenceNumber || null,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 61

```typescript
    });

    // Update device statuses based on maintenance result
    if (newOrder.items && Array.isArray(newOrder.items)) {
      for (const item of newOrder.items) {
        try {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 80

```typescript
          }

          await prisma.device.update({
            where: { id: item.deviceId },
            data: { 
              status: finalStatus,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 88

```typescript
          });

          // Create maintenance log entry
          await prisma.maintenanceLog.create({
            data: {
              deviceId: item.deviceId,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 89

```typescript

          // Create maintenance log entry
          await prisma.maintenanceLog.create({
            data: {
              deviceId: item.deviceId,
              model: item.model,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) استخدام تاريخ/وقت خام من العميل — السطر 93

```typescript
              deviceId: item.deviceId,
              model: item.model,
              repairDate: new Date(newOrder.date).toISOString(),
              notes: item.notes || item.fault || 'تم إنهاء الصيانة',
              result: item.result,
              status: 'pending'
```

⚠️ **الشرح:** استخدام `Date()` مباشرةً على قيم من العميل قد يؤدي لاختلاف المناطق الزمنية وفقدان الدقة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 100

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 115

```typescript
    return NextResponse.json(processedOrder, { status: 201 });
  } catch (error) {
    console.error('Failed to create delivery order:', error);
    return NextResponse.json({ error: 'Failed to create delivery order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 116

```typescript
  } catch (error) {
    console.error('Failed to create delivery order:', error);
    return NextResponse.json({ error: 'Failed to create delivery order' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 120

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedOrder = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 145

```typescript
    }

    // Update the delivery order
    const order = await prisma.deliveryOrder.update({
      where: { id: updatedOrder.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 146

```typescript

    // Update the delivery order
    const order = await prisma.deliveryOrder.update({
      where: { id: updatedOrder.id },
      data: {
        deliveryOrderNumber: updatedOrder.deliveryOrderNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 162

```typescript
    });
    
    // Update device statuses based on maintenance result
    if (updatedOrder.items && Array.isArray(updatedOrder.items)) {
      for (const item of updatedOrder.items) {
        try {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 181

```typescript
          }

          await prisma.device.update({
            where: { id: item.deviceId },
            data: { 
              status: finalStatus,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 189

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (18) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 204

```typescript
    return NextResponse.json(processedOrder);
  } catch (error) {
    console.error('Failed to update delivery order:', error);
    return NextResponse.json({ error: 'Failed to update delivery order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (19) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 205

```typescript
  } catch (error) {
    console.error('Failed to update delivery order:', error);
    return NextResponse.json({ error: 'Failed to update delivery order' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (20) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 209

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (21) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 209

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (22) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 225

```typescript
    }

    // Parse items to update device statuses back
    let items = [];
    try {
      items = typeof existingOrder.items === 'string' ?
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (23) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 231

```typescript
        JSON.parse(existingOrder.items) : existingOrder.items;
    } catch (error) {
      console.warn('Failed to parse items for device status update:', error);
    }

    // Update device statuses back to maintenance
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (24) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 234

```typescript
    }

    // Update device statuses back to maintenance
    if (Array.isArray(items)) {
      for (const item of items) {
        try {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (25) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 238

```typescript
      for (const item of items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: 'قيد الإصلاح' }
          });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (26) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 243

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (27) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 248

```typescript
    }

    // Delete the delivery order
    await prisma.deliveryOrder.delete({
      where: { id: parseInt(id) }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (28) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 249

```typescript

    // Delete the delivery order
    await prisma.deliveryOrder.delete({
      where: { id: parseInt(id) }
    });

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (29) استدعاء prisma.delete — السطر 249

```typescript

    // Delete the delivery order
    await prisma.deliveryOrder.delete({
      where: { id: parseInt(id) }
    });

```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (30) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 255

```typescript
    return NextResponse.json({ message: 'Delivery order deleted successfully' });
  } catch (error) {
    console.error('Failed to delete delivery order:', error);
    return NextResponse.json({ error: 'Failed to delete delivery order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (31) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 256

```typescript
  } catch (error) {
    console.error('Failed to delete delivery order:', error);
    return NextResponse.json({ error: 'Failed to delete delivery order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/devices/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 14

```typescript
}

export async function POST(request: Request) {
  try {
    const newDevice = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 38

```typescript
    }

    // Create new device
    const device = await prisma.device.create({
      data: {
        id: newDevice.id,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 39

```typescript

    // Create new device
    const device = await prisma.device.create({
      data: {
        id: newDevice.id,
        model: newDevice.model,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 56

```typescript
    return NextResponse.json(device, { status: 201 });
  } catch (error) {
    console.error('Failed to create device:', error);
    return NextResponse.json({ error: 'Failed to create device' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 57

```typescript
  } catch (error) {
    console.error('Failed to create device:', error);
    return NextResponse.json({ error: 'Failed to create device' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 61

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedDevice = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 81

```typescript
    }

    // Update device
    const device = await prisma.device.update({
      where: { id: updatedDevice.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 82

```typescript

    // Update device
    const device = await prisma.device.update({
      where: { id: updatedDevice.id },
      data: {
        model: updatedDevice.model || existingDevice.model,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 98

```typescript
    return NextResponse.json(device);
  } catch (error) {
    console.error('Failed to update device:', error);
    return NextResponse.json({ error: 'Failed to update device' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 99

```typescript
  } catch (error) {
    console.error('Failed to update device:', error);
    return NextResponse.json({ error: 'Failed to update device' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 103

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 103

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 123

```typescript
    }

    // Delete device
    await prisma.device.delete({
      where: { id }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 124

```typescript

    // Delete device
    await prisma.device.delete({
      where: { id }
    });

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) استدعاء prisma.delete — السطر 124

```typescript

    // Delete device
    await prisma.device.delete({
      where: { id }
    });

```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 130

```typescript
    return NextResponse.json({ message: 'Device deleted successfully' });
  } catch (error) {
    console.error('Failed to delete device:', error);
    return NextResponse.json({ error: 'Failed to delete device' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 131

```typescript
  } catch (error) {
    console.error('Failed to delete device:', error);
    return NextResponse.json({ error: 'Failed to delete device' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/evaluations/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 27

```typescript
}

export async function POST(request: Request) {
  try {
    const newEvaluation = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 51

```typescript
    }

    // Create the evaluation order in the database
    const evaluation = await prisma.evaluationOrder.create({
      data: {
        orderId: newEvaluation.orderId,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 52

```typescript

    // Create the evaluation order in the database
    const evaluation = await prisma.evaluationOrder.create({
      data: {
        orderId: newEvaluation.orderId,
        employeeName: newEvaluation.employeeName,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 76

```typescript
    return NextResponse.json(processedEvaluation, { status: 201 });
  } catch (error) {
    console.error('Failed to create evaluation:', error);
    return NextResponse.json({ error: 'Failed to create evaluation' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 77

```typescript
  } catch (error) {
    console.error('Failed to create evaluation:', error);
    return NextResponse.json({ error: 'Failed to create evaluation' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 81

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedEvaluation = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 97

```typescript
    }

    // Update the evaluation
    const evaluation = await prisma.evaluationOrder.update({
      where: { id: updatedEvaluation.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 98

```typescript

    // Update the evaluation
    const evaluation = await prisma.evaluationOrder.update({
      where: { id: updatedEvaluation.id },
      data: {
        orderId: updatedEvaluation.orderId,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 123

```typescript
    return NextResponse.json(processedEvaluation);
  } catch (error) {
    console.error('Failed to update evaluation:', error);
    return NextResponse.json({ error: 'Failed to update evaluation' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 124

```typescript
  } catch (error) {
    console.error('Failed to update evaluation:', error);
    return NextResponse.json({ error: 'Failed to update evaluation' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 128

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 128

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 144

```typescript
    }

    // Delete the evaluation
    await prisma.evaluationOrder.delete({
      where: { id: parseInt(id) }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 145

```typescript

    // Delete the evaluation
    await prisma.evaluationOrder.delete({
      where: { id: parseInt(id) }
    });
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) استدعاء prisma.delete — السطر 145

```typescript

    // Delete the evaluation
    await prisma.evaluationOrder.delete({
      where: { id: parseInt(id) }
    });
    
```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 151

```typescript
    return NextResponse.json({ message: 'Evaluation deleted successfully' });
  } catch (error) {
    console.error('Failed to delete evaluation:', error);
    return NextResponse.json({ error: 'Failed to delete evaluation' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 152

```typescript
  } catch (error) {
    console.error('Failed to delete evaluation:', error);
    return NextResponse.json({ error: 'Failed to delete evaluation' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/maintenance-logs/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 16

```typescript
}

export async function POST(request: Request) {
  try {
    const newLog = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 28

```typescript
    }

    // Create the maintenance log
    const log = await prisma.maintenanceLog.create({
      data: {
        deviceId: newLog.deviceId,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 29

```typescript

    // Create the maintenance log
    const log = await prisma.maintenanceLog.create({
      data: {
        deviceId: newLog.deviceId,
        model: newLog.model,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 45

```typescript
    return NextResponse.json(log, { status: 201 });
  } catch (error) {
    console.error('Failed to create maintenance log:', error);
    return NextResponse.json({ error: 'Failed to create maintenance log' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 46

```typescript
  } catch (error) {
    console.error('Failed to create maintenance log:', error);
    return NextResponse.json({ error: 'Failed to create maintenance log' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 50

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedLog = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 66

```typescript
    }

    // Update the log
    const log = await prisma.maintenanceLog.update({
      where: { id: updatedLog.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 67

```typescript

    // Update the log
    const log = await prisma.maintenanceLog.update({
      where: { id: updatedLog.id },
      data: {
        deviceId: updatedLog.deviceId,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 84

```typescript
    return NextResponse.json(log);
  } catch (error) {
    console.error('Failed to update maintenance log:', error);
    return NextResponse.json({ error: 'Failed to update maintenance log' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 85

```typescript
  } catch (error) {
    console.error('Failed to update maintenance log:', error);
    return NextResponse.json({ error: 'Failed to update maintenance log' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 89

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 89

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 105

```typescript
    }
    
    // Delete the log
    await prisma.maintenanceLog.delete({
      where: { id: parseInt(id) }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 106

```typescript
    
    // Delete the log
    await prisma.maintenanceLog.delete({
      where: { id: parseInt(id) }
    });
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) استدعاء prisma.delete — السطر 106

```typescript
    
    // Delete the log
    await prisma.maintenanceLog.delete({
      where: { id: parseInt(id) }
    });
    
```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 112

```typescript
    return NextResponse.json({ message: 'Maintenance log deleted successfully' });
  } catch (error) {
    console.error('Failed to delete maintenance log:', error);
    return NextResponse.json({ error: 'Failed to delete maintenance log' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 113

```typescript
  } catch (error) {
    console.error('Failed to delete maintenance log:', error);
    return NextResponse.json({ error: 'Failed to delete maintenance log' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/maintenance-orders/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 27

```typescript
}

export async function POST(request: Request) {
  try {
    const newOrder = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 51

```typescript
    }

    // Create the maintenance order in the database
    const order = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: newOrder.orderNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 52

```typescript

    // Create the maintenance order in the database
    const order = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: newOrder.orderNumber,
        referenceNumber: newOrder.referenceNumber || null,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 67

```typescript
    });

    // Update device statuses if items are provided
    if (newOrder.items && Array.isArray(newOrder.items)) {
      for (const item of newOrder.items) {
        try {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 71

```typescript
      for (const item of newOrder.items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId || item.id },
            data: { status: 'بانتظار استلام في الصيانة' }
          });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 76

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId || item.id}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 91

```typescript
    return NextResponse.json(processedOrder, { status: 201 });
  } catch (error) {
    console.error('Failed to create maintenance order:', error);
    return NextResponse.json({ error: 'Failed to create maintenance order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 92

```typescript
  } catch (error) {
    console.error('Failed to create maintenance order:', error);
    return NextResponse.json({ error: 'Failed to create maintenance order' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 96

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedOrder = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 112

```typescript
    }

    // Update the maintenance order
    const order = await prisma.maintenanceOrder.update({
      where: { id: updatedOrder.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 113

```typescript

    // Update the maintenance order
    const order = await prisma.maintenanceOrder.update({
      where: { id: updatedOrder.id },
      data: {
        orderNumber: updatedOrder.orderNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 139

```typescript
    return NextResponse.json(processedOrder);
  } catch (error) {
    console.error('Failed to update maintenance order:', error);
    return NextResponse.json({ error: 'Failed to update maintenance order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 140

```typescript
  } catch (error) {
    console.error('Failed to update maintenance order:', error);
    return NextResponse.json({ error: 'Failed to update maintenance order' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 144

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 144

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 160

```typescript
    }

    // Parse items to update device statuses back
    let items = [];
    try {
      items = typeof existingOrder.items === 'string' ?
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 166

```typescript
        JSON.parse(existingOrder.items) : existingOrder.items;
    } catch (error) {
      console.warn('Failed to parse items for device status update:', error);
    }

    // Update device statuses back to available
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (18) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 169

```typescript
    }

    // Update device statuses back to available
    if (Array.isArray(items)) {
      for (const item of items) {
        try {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (19) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 173

```typescript
      for (const item of items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId || item.id },
            data: { status: 'متاح للبيع' }
          });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (20) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 178

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId || item.id}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (21) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 183

```typescript
    }

    // Delete the maintenance order
    await prisma.maintenanceOrder.delete({
      where: { id: parseInt(id) }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (22) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 184

```typescript

    // Delete the maintenance order
    await prisma.maintenanceOrder.delete({
      where: { id: parseInt(id) }
    });

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (23) استدعاء prisma.delete — السطر 184

```typescript

    // Delete the maintenance order
    await prisma.maintenanceOrder.delete({
      where: { id: parseInt(id) }
    });

```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (24) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 190

```typescript
    return NextResponse.json({ message: 'Maintenance order deleted successfully' });
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);
    return NextResponse.json({ error: 'Failed to delete maintenance order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (25) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 191

```typescript
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);
    return NextResponse.json({ error: 'Failed to delete maintenance order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/maintenance-receipts/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 27

```typescript
}

export async function POST(request: Request) {
  try {
    const newReceipt = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 51

```typescript
    }

    // Create the maintenance receipt in the database
    const receipt = await prisma.maintenanceReceiptOrder.create({
      data: {
        receiptNumber: newReceipt.receiptNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 52

```typescript

    // Create the maintenance receipt in the database
    const receipt = await prisma.maintenanceReceiptOrder.create({
      data: {
        receiptNumber: newReceipt.receiptNumber,
        referenceNumber: newReceipt.referenceNumber || null,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 66

```typescript
    });

    // Update device statuses if items are provided
    if (newReceipt.items && Array.isArray(newReceipt.items)) {
      for (const item of newReceipt.items) {
        try {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 70

```typescript
      for (const item of newReceipt.items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId || item.id },
            data: { status: 'متاح للبيع' }
          });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 75

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId || item.id}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 90

```typescript
    return NextResponse.json(processedReceipt, { status: 201 });
  } catch (error) {
    console.error('Failed to create maintenance receipt:', error);
    return NextResponse.json({ error: 'Failed to create maintenance receipt' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 91

```typescript
  } catch (error) {
    console.error('Failed to create maintenance receipt:', error);
    return NextResponse.json({ error: 'Failed to create maintenance receipt' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 95

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedReceipt = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 111

```typescript
    }

    // Update the maintenance receipt
    const receipt = await prisma.maintenanceReceiptOrder.update({
      where: { id: updatedReceipt.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 112

```typescript

    // Update the maintenance receipt
    const receipt = await prisma.maintenanceReceiptOrder.update({
      where: { id: updatedReceipt.id },
      data: {
        receiptNumber: updatedReceipt.receiptNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 137

```typescript
    return NextResponse.json(processedReceipt);
  } catch (error) {
    console.error('Failed to update maintenance receipt:', error);
    return NextResponse.json({ error: 'Failed to update maintenance receipt' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 138

```typescript
  } catch (error) {
    console.error('Failed to update maintenance receipt:', error);
    return NextResponse.json({ error: 'Failed to update maintenance receipt' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 142

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 142

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 158

```typescript
    }

    // Parse items to update device statuses back
    let items = [];
    try {
      items = typeof existingReceipt.items === 'string' ?
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 164

```typescript
        JSON.parse(existingReceipt.items) : existingReceipt.items;
    } catch (error) {
      console.warn('Failed to parse items for device status update:', error);
    }

    // Update device statuses back to maintenance
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (18) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 167

```typescript
    }

    // Update device statuses back to maintenance
    if (Array.isArray(items)) {
      for (const item of items) {
        try {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (19) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 171

```typescript
      for (const item of items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId || item.id },
            data: { status: 'قيد الإصلاح' }
          });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (20) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 176

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId || item.id}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (21) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 181

```typescript
    }

    // Delete the maintenance receipt
    await prisma.maintenanceReceiptOrder.delete({
      where: { id: parseInt(id) }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (22) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 182

```typescript

    // Delete the maintenance receipt
    await prisma.maintenanceReceiptOrder.delete({
      where: { id: parseInt(id) }
    });

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (23) استدعاء prisma.delete — السطر 182

```typescript

    // Delete the maintenance receipt
    await prisma.maintenanceReceiptOrder.delete({
      where: { id: parseInt(id) }
    });

```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (24) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 188

```typescript
    return NextResponse.json({ message: 'Maintenance receipt deleted successfully' });
  } catch (error) {
    console.error('Failed to delete maintenance receipt:', error);
    return NextResponse.json({ error: 'Failed to delete maintenance receipt' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (25) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 189

```typescript
  } catch (error) {
    console.error('Failed to delete maintenance receipt:', error);
    return NextResponse.json({ error: 'Failed to delete maintenance receipt' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/returns/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 29

```typescript
}

export async function POST(request: Request) {
  try {
    const newReturn = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 41

```typescript
    
    // إنشاء المرتجع في قاعدة البيانات
    const returnRecord = await prisma.return.create({
      data: {
        roNumber: `RO-${newId}`,
        opReturnNumber: newReturn.opReturnNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 72

```typescript
          }
          
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: newStatus }
          });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 77

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 95

```typescript
    return NextResponse.json(processedReturn, { status: 201 });
  } catch (error) {
    console.error('Failed to create return:', error);
    return NextResponse.json({ error: 'Failed to create return' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 96

```typescript
  } catch (error) {
    console.error('Failed to create return:', error);
    return NextResponse.json({ error: 'Failed to create return' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 100

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedReturn = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 114

```typescript
    
    // تحديث المرتجع
    const returnRecord = await prisma.return.update({
      where: { id: updatedReturn.id },
      data: {
        opReturnNumber: updatedReturn.opReturnNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 146

```typescript
    return NextResponse.json(processedReturn);
  } catch (error) {
    console.error('Failed to update return:', error);
    return NextResponse.json({ error: 'Failed to update return' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 147

```typescript
  } catch (error) {
    console.error('Failed to update return:', error);
    return NextResponse.json({ error: 'Failed to update return' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 151

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 151

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 168

```typescript
      for (const item of existingReturn.items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: 'مباع' }
          });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 179

```typescript
    
    // حذف المرتجع
    await prisma.return.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) استدعاء prisma.delete — السطر 179

```typescript
    
    // حذف المرتجع
    await prisma.return.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 185

```typescript
    return NextResponse.json({ message: 'Return deleted successfully' });
  } catch (error) {
    console.error('Failed to delete return:', error);
    return NextResponse.json({ error: 'Failed to delete return' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 186

```typescript
  } catch (error) {
    console.error('Failed to delete return:', error);
    return NextResponse.json({ error: 'Failed to delete return' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/sales/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 28

```typescript
}

export async function POST(request: Request) {
  try {
    const newSale = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 39

```typescript
    const newId = (maxIdRecord?.id || 0) + 1;
    
    // Create the sale in the database
    const sale = await prisma.sale.create({
      data: {
        soNumber: `SO-${newId}`,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 40

```typescript
    
    // Create the sale in the database
    const sale = await prisma.sale.create({
      data: {
        soNumber: `SO-${newId}`,
        opNumber: newSale.opNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 55

```typescript
    });
    
    // Update device statuses
    if (newSale.items && Array.isArray(newSale.items)) {
      for (const item of newSale.items) {
        try {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 59

```typescript
      for (const item of newSale.items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: 'مباع' }
          });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 64

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 82

```typescript
    return NextResponse.json(processedSale, { status: 201 });
  } catch (error) {
    console.error('Failed to create sale:', error);
    return NextResponse.json({ error: 'Failed to create sale' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 83

```typescript
  } catch (error) {
    console.error('Failed to create sale:', error);
    return NextResponse.json({ error: 'Failed to create sale' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 87

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedSale = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 112

```typescript
    for (const deviceId of removedDeviceIds) {
      try {
        await prisma.device.update({
          where: { id: deviceId },
          data: { status: 'متاح للبيع' }
        });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 117

```typescript
        });
      } catch (deviceError) {
        console.error(`Failed to update device ${deviceId}:`, deviceError);
      }
    }
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 124

```typescript
    for (const item of updatedSale.items) {
      try {
        await prisma.device.update({
          where: { id: item.deviceId },
          data: { status: 'مباع' }
        });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 129

```typescript
        });
      } catch (deviceError) {
        console.error(`Failed to update device ${item.deviceId}:`, deviceError);
      }
    }
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 133

```typescript
    }
    
    // Update the sale
    const sale = await prisma.sale.update({
      where: { id: updatedSale.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 134

```typescript
    
    // Update the sale
    const sale = await prisma.sale.update({
      where: { id: updatedSale.id },
      data: {
        opNumber: updatedSale.opNumber,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 162

```typescript
    return NextResponse.json(processedSale);
  } catch (error) {
    console.error('Failed to update sale:', error);
    return NextResponse.json({ error: 'Failed to update sale' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 163

```typescript
  } catch (error) {
    console.error('Failed to update sale:', error);
    return NextResponse.json({ error: 'Failed to update sale' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (18) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 167

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (19) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 167

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (20) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 185

```typescript
      for (const item of items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: 'متاح للبيع' }
          });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (21) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 190

```typescript
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (22) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 195

```typescript
    }
    
    // Delete the sale
    await prisma.sale.delete({
      where: { id }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (23) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 196

```typescript
    
    // Delete the sale
    await prisma.sale.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (24) استدعاء prisma.delete — السطر 196

```typescript
    
    // Delete the sale
    await prisma.sale.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (25) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 202

```typescript
    return NextResponse.json({ message: 'Sale deleted successfully' });
  } catch (error) {
    console.error('Failed to delete sale:', error);
    return NextResponse.json({ error: 'Failed to delete sale' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (26) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 203

```typescript
  } catch (error) {
    console.error('Failed to delete sale:', error);
    return NextResponse.json({ error: 'Failed to delete sale' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/settings/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 30

```typescript
}

export async function PUT(request: Request) {
  try {
    const data: Partial<SystemSettings> = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 46

```typescript

    // في التطبيق الحقيقي، ستحفظ في قاعدة البيانات هنا
    // await prisma.systemSetting.update({
    //   where: { id: 1 },
    //   data,
    // });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/suppliers/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 17

```typescript
}

export async function POST(request: Request) {
  try {
    const newSupplier = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 42

```typescript
    
    // إنشاء المورد في قاعدة البيانات
    const supplier = await prisma.supplier.create({
      data: {
        name: newSupplier.name,
        phone: newSupplier.phone || '',
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 52

```typescript
    return NextResponse.json(supplier, { status: 201 });
  } catch (error) {
    console.error('Failed to create supplier:', error);
    return NextResponse.json({ error: 'Failed to create supplier' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 53

```typescript
  } catch (error) {
    console.error('Failed to create supplier:', error);
    return NextResponse.json({ error: 'Failed to create supplier' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 57

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedSupplier = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 85

```typescript
    
    // تحديث المورد
    const supplier = await prisma.supplier.update({
      where: { id: updatedSupplier.id },
      data: {
        name: updatedSupplier.name,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 96

```typescript
    return NextResponse.json(supplier);
  } catch (error) {
    console.error('Failed to update supplier:', error);
    return NextResponse.json({ error: 'Failed to update supplier' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 97

```typescript
  } catch (error) {
    console.error('Failed to update supplier:', error);
    return NextResponse.json({ error: 'Failed to update supplier' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 101

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 101

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 127

```typescript
    if (relatedOperations.length > 0) {
      return NextResponse.json({
        error: 'Cannot delete supplier',
        reason: 'يوجد عمليات مرتبطة بهذا المورد',
        relatedOperations
      }, { status: 409 });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 134

```typescript
    
    // حذف المورد
    await prisma.supplier.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (13) استدعاء prisma.delete — السطر 134

```typescript
    
    // حذف المورد
    await prisma.supplier.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 140

```typescript
    return NextResponse.json({ message: 'Supplier deleted successfully' });
  } catch (error) {
    console.error('Failed to delete supplier:', error);
    return NextResponse.json({ error: 'Failed to delete supplier' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 141

```typescript
  } catch (error) {
    console.error('Failed to delete supplier:', error);
    return NextResponse.json({ error: 'Failed to delete supplier' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/supply/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 16

```typescript
}

export async function POST(request: Request) {
  try {
    const newOrder = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 27

```typescript
    const newId = (maxIdRecord?.id || 0) + 1;
    
    // Create the order in the database
    const order = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `SUP-${newId}`,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 28

```typescript
    
    // Create the order in the database
    const order = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `SUP-${newId}`,
        supplierId: newOrder.supplierId,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 54

```typescript
          
          if (!existingDevice) {
            await prisma.device.create({
              data: {
                id: item.imei,
                model: `${item.manufacturer} ${item.model}`,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 68

```typescript
          }
        } catch (deviceError) {
          console.error('Failed to create device:', deviceError);
        }
      }
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 75

```typescript
    return NextResponse.json(order, { status: 201 });
  } catch (error) {
    console.error('Failed to create supply order:', error);
    return NextResponse.json({ error: 'Failed to create supply order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 76

```typescript
  } catch (error) {
    console.error('Failed to create supply order:', error);
    return NextResponse.json({ error: 'Failed to create supply order' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 80

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedOrder = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 93

```typescript
    }
    
    // Update the order
    const order = await prisma.supplyOrder.update({
      where: { id: updatedOrder.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 94

```typescript
    
    // Update the order
    const order = await prisma.supplyOrder.update({
      where: { id: updatedOrder.id },
      data: {
        supplierId: updatedOrder.supplierId,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 115

```typescript
    return NextResponse.json(order);
  } catch (error) {
    console.error('Failed to update supply order:', error);
    return NextResponse.json({ error: 'Failed to update supply order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 116

```typescript
  } catch (error) {
    console.error('Failed to update supply order:', error);
    return NextResponse.json({ error: 'Failed to update supply order' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 120

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 120

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (15) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 133

```typescript
    }
    
    // Delete the order
    await prisma.supplyOrder.delete({
      where: { id }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 134

```typescript
    
    // Delete the order
    await prisma.supplyOrder.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) استدعاء prisma.delete — السطر 134

```typescript
    
    // Delete the order
    await prisma.supplyOrder.delete({
      where: { id }
    });
    
```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (18) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 140

```typescript
    return NextResponse.json({ message: 'Supply order deleted successfully' });
  } catch (error) {
    console.error('Failed to delete supply order:', error);
    return NextResponse.json({ error: 'Failed to delete supply order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (19) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 141

```typescript
  } catch (error) {
    console.error('Failed to delete supply order:', error);
    return NextResponse.json({ error: 'Failed to delete supply order' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/upload/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 6

```typescript
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

## ملف: `app/api/users/reset-password/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 29

```typescript
}

export async function POST(request: Request) {
  const { userId, username, email } = await request.json();

  if (!userId || !username || !email) {
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

## ملف: `app/api/users/route.ts`

### (1) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 11

```typescript
) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        username,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (2) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 46

```typescript
}

export async function POST(request: Request) {
  try {
    const newUser = await request.json();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 70

```typescript
    }

    // Create new user
    const user = await prisma.user.create({
      data: {
        name: newUser.name,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 71

```typescript

    // Create new user
    const user = await prisma.user.create({
      data: {
        name: newUser.name,
        email: newUser.email,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 106

```typescript
    return NextResponse.json(processedUser, { status: 201 });
  } catch (error) {
    console.error('Failed to create user:', error);
    return NextResponse.json({ error: 'Failed to create user' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 107

```typescript
  } catch (error) {
    console.error('Failed to create user:', error);
    return NextResponse.json({ error: 'Failed to create user' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (7) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 111

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedUser = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 145

```typescript
    }

    // Update user
    const user = await prisma.user.update({
      where: { id: updatedUser.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 146

```typescript

    // Update user
    const user = await prisma.user.update({
      where: { id: updatedUser.id },
      data: {
        name: updatedUser.name || existingUser.name,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 183

```typescript
    return NextResponse.json(processedUser);
  } catch (error) {
    console.error('Failed to update user:', error);
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 184

```typescript
  } catch (error) {
    console.error('Failed to update user:', error);
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 188

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (13) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 188

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 202

```typescript
    if (id === 1) {
      return NextResponse.json(
        { message: 'Cannot delete Super Admin' },
        { status: 403 },
      );
    }
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 216

```typescript
    }

    // Delete user
    await prisma.user.delete({
      where: { id }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (16) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 217

```typescript

    // Delete user
    await prisma.user.delete({
      where: { id }
    });

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (17) استدعاء prisma.delete — السطر 217

```typescript

    // Delete user
    await prisma.user.delete({
      where: { id }
    });

```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (18) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 230

```typescript
    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Failed to delete user:', error);
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (19) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 231

```typescript
  } catch (error) {
    console.error('Failed to delete user:', error);
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف: `app/api/warehouses/route.ts`

### (1) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 14

```typescript
}

export async function POST(request: Request) {
  try {
    const newWarehouse = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (2) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 26

```typescript
    }

    // Create new warehouse
    const warehouse = await prisma.warehouse.create({
      data: {
        name: newWarehouse.name,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (3) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 27

```typescript

    // Create new warehouse
    const warehouse = await prisma.warehouse.create({
      data: {
        name: newWarehouse.name,
        type: newWarehouse.type,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (4) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 37

```typescript
    return NextResponse.json(warehouse, { status: 201 });
  } catch (error) {
    console.error('Failed to create warehouse:', error);
    return NextResponse.json({ error: 'Failed to create warehouse' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (5) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 38

```typescript
  } catch (error) {
    console.error('Failed to create warehouse:', error);
    return NextResponse.json({ error: 'Failed to create warehouse' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (6) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 42

```typescript
}

export async function PUT(request: Request) {
  try {
    const updatedWarehouse = await request.json();
    
```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (7) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 62

```typescript
    }

    // Update warehouse
    const warehouse = await prisma.warehouse.update({
      where: { id: updatedWarehouse.id },
      data: {
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (8) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 63

```typescript

    // Update warehouse
    const warehouse = await prisma.warehouse.update({
      where: { id: updatedWarehouse.id },
      data: {
        name: updatedWarehouse.name || existingWarehouse.name,
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (9) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 74

```typescript
    return NextResponse.json(warehouse);
  } catch (error) {
    console.error('Failed to update warehouse:', error);
    return NextResponse.json({ error: 'Failed to update warehouse' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (10) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 75

```typescript
  } catch (error) {
    console.error('Failed to update warehouse:', error);
    return NextResponse.json({ error: 'Failed to update warehouse' }, { status: 500 });
  }
}

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (11) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 79

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (12) غياب تعريف لدور المستخدم قبل معالج الطلب — السطر 79

```typescript
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

```

⚠️ **الشرح:** لا توجد طبقة تفويض (Authorization) تحدد من يحق له هذا الطلب، ما يعرّضك لهجمات كسر التحكم بالوصول.

### (13) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 136

```typescript
    if (relatedOperations.length > 0) {
      return NextResponse.json({
        error: 'Cannot delete warehouse',
        reason: 'يوجد عمليات أو أجهزة مرتبطة بهذا المخزن',
        relatedOperations
      }, { status: 409 });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (14) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 142

```typescript
    }

    // Delete warehouse
    await prisma.warehouse.delete({
      where: { id }
    });
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (15) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 143

```typescript

    // Delete warehouse
    await prisma.warehouse.delete({
      where: { id }
    });

```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (16) استدعاء prisma.delete — السطر 143

```typescript

    // Delete warehouse
    await prisma.warehouse.delete({
      where: { id }
    });

```

⚠️ **الشرح:** الحذف مباشرةً دون تحقق من العلاقات أو وجود أوامر لاحقة قد يتسبب في مراجع يتيمة.

### (17) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 149

```typescript
    return NextResponse.json({ message: 'Warehouse deleted successfully' });
  } catch (error) {
    console.error('Failed to delete warehouse:', error);
    return NextResponse.json({ error: 'Failed to delete warehouse' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

### (18) عملية حذف/تعديل دون استخدام معاملة ($transaction) — السطر 150

```typescript
  } catch (error) {
    console.error('Failed to delete warehouse:', error);
    return NextResponse.json({ error: 'Failed to delete warehouse' }, { status: 500 });
  }
}
```

⚠️ **الشرح:** تنفيذ عملية حساسة (حذف أو تعديل) خارج `prisma.$transaction` قد يؤدي إلى فقدان الاتساق إذا فشل جزء من السلسلة.

## ملف قاعدة البيانات: `generated/prisma/schema.prisma`

### (1) علاقة بلا onDelete مقيَّد — السطر 26

```prisma
@relation(fields: [authorId], references: [id])```

⚠️ **الشرح:** ينصح بتحديد `onDelete: Restrict` أو `Cascade` بوضوح لمنع الحذف العرضي للسجلات المرتبطة.

## ملف قاعدة البيانات: `prisma/schema.prisma`

### (1) علاقة بلا onDelete مقيَّد — السطر 36

```prisma
@relation(fields: [authorId], references: [id])```

⚠️ **الشرح:** ينصح بتحديد `onDelete: Restrict` أو `Cascade` بوضوح لمنع الحذف العرضي للسجلات المرتبطة.


## التوصيات العامة
1. **استخدام المعاملات (Transactions)**: غلّف كل سلسلة أوامر حساسة بـ `prisma.$transaction([...])`.
2. **طبقة تفويض موحدة (RBAC)**: أضف Middleware مثل `withRole(['Admin','Maintenance'])` لكل مسار.
3. **قيود الحذف (Foreign Keys)**: حدد `onDelete: Restrict` للعلاقات الحرجة.
4. **تدقيق المخزون**: قبل وبعد أي عملية تؤثر على الكمية، تحقق من عدم تحول الكمية إلى قيمة سالبة.
5. **توحيد التوقيت**: خزّن التواريخ بـ UTC وأعدها للعميل بالتوقيت المحلي (Asia/Aden).
6. **حقول التدقيق**: أضف `createdBy`, `updatedBy`, `createdAt`, `updatedAt` في جميع الجداول.
