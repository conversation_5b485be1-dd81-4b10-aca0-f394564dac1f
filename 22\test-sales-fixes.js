// سكريبت اختبار إصلاحات صفحة المبيعات
// Test script for sales page fixes

async function testSalesFixes() {
  console.log('🧪 اختبار إصلاحات صفحة المبيعات...\n');

  const authHeader = { 'Authorization': `Bearer ${btoa('user:admin:admin')}` };
  let allTestsPassed = true;

  // 1. اختبار إنشاء مبيعة جديدة
  console.log('1️⃣ اختبار إنشاء مبيعة جديدة...');
  try {
    const saleData = {
      opNumber: 'TEST-SALE-001',
      date: new Date().toISOString().slice(0, 16),
      clientName: 'عميل تجريبي',
      warehouseName: 'المخزن الرئيسي',
      items: [
        {
          deviceId: '111111111111111',
          model: 'iPhone 13 128GB',
          price: 1000,
          condition: 'جديد'
        }
      ],
      notes: 'اختبار إصلاحات المبيعات',
      warrantyPeriod: '1y',
      employeeName: 'مدير النظام',
      attachments: []
    };

    const response = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...authHeader
      },
      body: JSON.stringify(saleData)
    });

    if (response.ok) {
      const sale = await response.json();
      console.log('   ✅ تم إنشاء المبيعة:', sale.soNumber);
      console.log('   ✅ opNumber:', sale.opNumber);
      console.log('   ✅ employeeName:', sale.employeeName);
    } else {
      const error = await response.text();
      throw new Error(`فشل إنشاء المبيعة: ${error}`);
    }
  } catch (error) {
    console.log('   ❌ فشل اختبار إنشاء المبيعة:', error.message);
    allTestsPassed = false;
  }

  // 2. اختبار رفع المرفقات للمبيعات
  console.log('\n2️⃣ اختبار رفع المرفقات للمبيعات...');
  try {
    const testFile = new File(['test content'], 'test-sale.txt', { type: 'text/plain' });
    const formData = new FormData();
    formData.append('files', testFile);
    formData.append('section', 'sales');

    const uploadResponse = await fetch('http://localhost:9005/api/upload', {
      method: 'POST',
      headers: authHeader,
      body: formData
    });

    if (uploadResponse.ok) {
      const result = await uploadResponse.json();
      console.log('   ✅ تم رفع الملف للمبيعات:', result.files[0].originalName);
    } else {
      const error = await uploadResponse.text();
      throw new Error(`فشل رفع الملف: ${error}`);
    }
  } catch (error) {
    console.log('   ❌ فشل اختبار رفع المرفقات:', error.message);
    allTestsPassed = false;
  }

  // 3. اختبار تحديث حالة الأجهزة
  console.log('\n3️⃣ اختبار تحديث حالة الأجهزة...');
  try {
    const devicesResponse = await fetch('http://localhost:9005/api/devices', {
      headers: authHeader
    });

    if (devicesResponse.ok) {
      const devices = await devicesResponse.json();
      const testDevice = devices.find(d => d.id === '111111111111111');
      
      if (testDevice && testDevice.status === 'مباع') {
        console.log('   ✅ حالة الجهاز محدثة بشكل صحيح:', testDevice.status);
      } else if (testDevice) {
        console.log('   ⚠️ حالة الجهاز:', testDevice.status, '(قد تكون صحيحة حسب السياق)');
      } else {
        console.log('   ⚠️ الجهاز غير موجود في المخزون');
      }
    } else {
      throw new Error('فشل جلب بيانات الأجهزة');
    }
  } catch (error) {
    console.log('   ❌ فشل اختبار حالة الأجهزة:', error.message);
    allTestsPassed = false;
  }

  // 4. اختبار جلب المبيعات
  console.log('\n4️⃣ اختبار جلب المبيعات...');
  try {
    const salesResponse = await fetch('http://localhost:9005/api/sales', {
      headers: authHeader
    });

    if (salesResponse.ok) {
      const sales = await salesResponse.json();
      const testSale = sales.find(s => s.opNumber === 'TEST-SALE-001');
      
      if (testSale) {
        console.log('   ✅ المبيعة موجودة في قاعدة البيانات');
        console.log('   ✅ البيانات محفوظة بشكل صحيح');
      } else {
        console.log('   ⚠️ المبيعة التجريبية غير موجودة');
      }
    } else {
      throw new Error('فشل جلب بيانات المبيعات');
    }
  } catch (error) {
    console.log('   ❌ فشل اختبار جلب المبيعات:', error.message);
    allTestsPassed = false;
  }

  // تلخيص النتائج
  console.log('\n📊 تلخيص النتائج:');
  if (allTestsPassed) {
    console.log('🎉 جميع الاختبارات نجحت! إصلاحات المبيعات تعمل بشكل صحيح.');
  } else {
    console.log('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
  }

  console.log('\n✅ الإصلاحات المطبقة على المبيعات:');
  console.log('   • إصلاح رفع المرفقات مع التفويض');
  console.log('   • إصلاح مشكلة opNumber في قاعدة البيانات');
  console.log('   • حماية اسم الموظف');
  console.log('   • فحص العلاقات قبل الحذف');
  console.log('   • مزامنة حالة الأجهزة');
  console.log('   • تحسين معالجة البيانات');
}

// تشغيل الاختبار
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  testSalesFixes();
} else {
  // Browser environment
  console.log('يمكن تشغيل هذا السكريبت في وحدة تحكم المتصفح');
  window.testSalesFixes = testSalesFixes;
}
