'use client';

import { PermissionGuard, ActionGuard, ViewGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';
import { useStore } from '@/context/store';
import { UserSwitcher } from '@/components/UserSwitcher';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Info, Plus, Edit, Trash2, Eye } from 'lucide-react';

/**
 * صفحة تجريبية لتوضيح كيفية عمل نظام الصلاحيات
 */
export default function DemoPermissionsPage() {
  const { currentUser } = useStore();
  const supplyPermissions = usePermission('supply');
  const salesPermissions = usePermission('sales');
  const maintenancePermissions = usePermission('maintenance');

  return (
    <PermissionGuard pageKey="dashboard">
      <div className="space-y-6 p-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">تجربة نظام الصلاحيات</h1>
          <p className="text-muted-foreground">
            هذه الصفحة توضح كيفية عمل نظام إخفاء الصفحات والمكونات بناءً على صلاحيات المستخدم
          </p>
        </div>

        {/* تبديل المستخدم */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <UserSwitcher />

          {/* معلومات المستخدم الحالي */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                معلومات المستخدم الحالي
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div>
                  <p className="text-sm font-medium">الاسم:</p>
                  <p className="text-lg">{currentUser?.name || 'غير محدد'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">اسم المستخدم:</p>
                  <p className="text-lg">{currentUser?.username || 'غير محدد'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* صلاحيات التوريد */}
        <Card>
          <CardHeader>
            <CardTitle>صلاحيات التوريد</CardTitle>
            <CardDescription>
              هذا القسم يظهر فقط إذا كان لديك صلاحية عرض التوريد
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ViewGuard pageKey="supply">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge variant={supplyPermissions.canView ? "default" : "secondary"}>
                    <Eye className="h-3 w-3 mr-1" />
                    عرض: {supplyPermissions.canView ? "مسموح" : "غير مسموح"}
                  </Badge>
                  <Badge variant={supplyPermissions.canCreate ? "default" : "secondary"}>
                    <Plus className="h-3 w-3 mr-1" />
                    إنشاء: {supplyPermissions.canCreate ? "مسموح" : "غير مسموح"}
                  </Badge>
                  <Badge variant={supplyPermissions.canEdit ? "default" : "secondary"}>
                    <Edit className="h-3 w-3 mr-1" />
                    تعديل: {supplyPermissions.canEdit ? "مسموح" : "غير مسموح"}
                  </Badge>
                  <Badge variant={supplyPermissions.canDelete ? "default" : "secondary"}>
                    <Trash2 className="h-3 w-3 mr-1" />
                    حذف: {supplyPermissions.canDelete ? "مسموح" : "غير مسموح"}
                  </Badge>
                </div>

                <div className="flex gap-2">
                  <ActionGuard pageKey="supply" action="create">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      إنشاء أمر توريد
                    </Button>
                  </ActionGuard>
                  
                  <ActionGuard pageKey="supply" action="edit">
                    <Button variant="outline">
                      <Edit className="h-4 w-4 mr-2" />
                      تعديل أمر
                    </Button>
                  </ActionGuard>
                  
                  <ActionGuard pageKey="supply" action="delete">
                    <Button variant="destructive">
                      <Trash2 className="h-4 w-4 mr-2" />
                      حذف أمر
                    </Button>
                  </ActionGuard>
                </div>
              </div>
            </ViewGuard>

            <ViewGuard 
              pageKey="supply" 
              fallback={
                <Alert>
                  <AlertDescription>
                    ليس لديك صلاحية لعرض قسم التوريد
                  </AlertDescription>
                </Alert>
              }
            ><div></div></ViewGuard>
          </CardContent>
        </Card>

        {/* صلاحيات المبيعات */}
        <Card>
          <CardHeader>
            <CardTitle>صلاحيات المبيعات</CardTitle>
            <CardDescription>
              هذا القسم يظهر فقط إذا كان لديك صلاحية عرض المبيعات
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ViewGuard pageKey="sales">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge variant={salesPermissions.canView ? "default" : "secondary"}>
                    <Eye className="h-3 w-3 mr-1" />
                    عرض: {salesPermissions.canView ? "مسموح" : "غير مسموح"}
                  </Badge>
                  <Badge variant={salesPermissions.canCreate ? "default" : "secondary"}>
                    <Plus className="h-3 w-3 mr-1" />
                    إنشاء: {salesPermissions.canCreate ? "مسموح" : "غير مسموح"}
                  </Badge>
                  <Badge variant={salesPermissions.canEdit ? "default" : "secondary"}>
                    <Edit className="h-3 w-3 mr-1" />
                    تعديل: {salesPermissions.canEdit ? "مسموح" : "غير مسموح"}
                  </Badge>
                  <Badge variant={salesPermissions.canDelete ? "default" : "secondary"}>
                    <Trash2 className="h-3 w-3 mr-1" />
                    حذف: {salesPermissions.canDelete ? "مسموح" : "غير مسموح"}
                  </Badge>
                </div>

                <div className="flex gap-2">
                  <ActionGuard pageKey="sales" action="create">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      إنشاء فاتورة مبيعات
                    </Button>
                  </ActionGuard>
                  
                  <ActionGuard pageKey="sales" action="edit">
                    <Button variant="outline">
                      <Edit className="h-4 w-4 mr-2" />
                      تعديل فاتورة
                    </Button>
                  </ActionGuard>
                  
                  <ActionGuard pageKey="sales" action="delete">
                    <Button variant="destructive">
                      <Trash2 className="h-4 w-4 mr-2" />
                      حذف فاتورة
                    </Button>
                  </ActionGuard>
                </div>
              </div>
            </ViewGuard>

            <ViewGuard 
              pageKey="sales" 
              fallback={
                <Alert>
                  <AlertDescription>
                    ليس لديك صلاحية لعرض قسم المبيعات
                  </AlertDescription>
                </Alert>
              }
            ><div></div></ViewGuard>
          </CardContent>
        </Card>

        {/* صلاحيات الصيانة */}
        <Card>
          <CardHeader>
            <CardTitle>صلاحيات الصيانة</CardTitle>
            <CardDescription>
              هذا القسم يظهر فقط إذا كان لديك صلاحية عرض الصيانة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ViewGuard pageKey="maintenance">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge variant={maintenancePermissions.canView ? "default" : "secondary"}>
                    <Eye className="h-3 w-3 mr-1" />
                    عرض: {maintenancePermissions.canView ? "مسموح" : "غير مسموح"}
                  </Badge>
                  <Badge variant={maintenancePermissions.canCreate ? "default" : "secondary"}>
                    <Plus className="h-3 w-3 mr-1" />
                    إنشاء: {maintenancePermissions.canCreate ? "مسموح" : "غير مسموح"}
                  </Badge>
                  <Badge variant={maintenancePermissions.canEdit ? "default" : "secondary"}>
                    <Edit className="h-3 w-3 mr-1" />
                    تعديل: {maintenancePermissions.canEdit ? "مسموح" : "غير مسموح"}
                  </Badge>
                  <Badge variant={maintenancePermissions.canDelete ? "default" : "secondary"}>
                    <Trash2 className="h-3 w-3 mr-1" />
                    حذف: {maintenancePermissions.canDelete ? "مسموح" : "غير مسموح"}
                  </Badge>
                </div>

                <div className="flex gap-2">
                  <ActionGuard pageKey="maintenance" action="create">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      إنشاء أمر صيانة
                    </Button>
                  </ActionGuard>
                  
                  <ActionGuard pageKey="maintenance" action="edit">
                    <Button variant="outline">
                      <Edit className="h-4 w-4 mr-2" />
                      تعديل أمر
                    </Button>
                  </ActionGuard>
                  
                  <ActionGuard pageKey="maintenance" action="delete">
                    <Button variant="destructive">
                      <Trash2 className="h-4 w-4 mr-2" />
                      حذف أمر
                    </Button>
                  </ActionGuard>
                </div>
              </div>
            </ViewGuard>

            <ViewGuard 
              pageKey="maintenance" 
              fallback={
                <Alert>
                  <AlertDescription>
                    ليس لديك صلاحية لعرض قسم الصيانة
                  </AlertDescription>
                </Alert>
              }
            ><div></div></ViewGuard>
          </CardContent>
        </Card>

        {/* تعليمات الاستخدام */}
        <Card>
          <CardHeader>
            <CardTitle>كيفية الاستخدام</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">1. إخفاء عناصر القائمة:</h4>
              <p className="text-sm text-muted-foreground">
                عناصر القائمة الجانبية تختفي تلقائياً إذا لم تكن لديك صلاحية العرض
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">2. حماية الصفحات:</h4>
              <p className="text-sm text-muted-foreground">
                استخدم مكون PermissionGuard لحماية صفحة كاملة
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">3. حماية الأزرار والإجراءات:</h4>
              <p className="text-sm text-muted-foreground">
                استخدم مكون ActionGuard لإخفاء أزرار الإنشاء والتعديل والحذف
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </PermissionGuard>
  );
}
