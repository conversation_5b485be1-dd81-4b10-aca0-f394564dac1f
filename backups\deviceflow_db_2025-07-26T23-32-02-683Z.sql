--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: AuditLog; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."AuditLog" (
    id integer NOT NULL,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "userId" integer NOT NULL,
    username text NOT NULL,
    operation text NOT NULL,
    details text NOT NULL
);


ALTER TABLE public."AuditLog" OWNER TO deviceflow_user;

--
-- Name: AuditLog_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."AuditLog_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."AuditLog_id_seq" OWNER TO deviceflow_user;

--
-- Name: AuditLog_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."AuditLog_id_seq" OWNED BY public."AuditLog".id;


--
-- Name: Client; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Client" (
    id integer NOT NULL,
    name text NOT NULL,
    phone text,
    email text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Client" OWNER TO deviceflow_user;

--
-- Name: Client_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Client_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Client_id_seq" OWNER TO deviceflow_user;

--
-- Name: Client_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Client_id_seq" OWNED BY public."Client".id;


--
-- Name: DeliveryOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."DeliveryOrder" (
    id integer NOT NULL,
    "deliveryOrderNumber" text NOT NULL,
    "referenceNumber" text,
    date text NOT NULL,
    "warehouseId" integer NOT NULL,
    "warehouseName" text NOT NULL,
    "employeeName" text NOT NULL,
    items jsonb NOT NULL,
    notes text,
    "attachmentName" text,
    status text DEFAULT 'completed'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."DeliveryOrder" OWNER TO deviceflow_user;

--
-- Name: DeliveryOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."DeliveryOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."DeliveryOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: DeliveryOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."DeliveryOrder_id_seq" OWNED BY public."DeliveryOrder".id;


--
-- Name: Device; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Device" (
    id text NOT NULL,
    model text NOT NULL,
    status text NOT NULL,
    storage text NOT NULL,
    price double precision NOT NULL,
    condition text NOT NULL,
    "warehouseId" integer,
    "supplierId" integer,
    "dateAdded" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "replacementInfo" jsonb
);


ALTER TABLE public."Device" OWNER TO deviceflow_user;

--
-- Name: DeviceModel; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."DeviceModel" (
    id integer NOT NULL,
    name text NOT NULL,
    "manufacturerId" integer NOT NULL,
    category text DEFAULT 'هاتف ذكي'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."DeviceModel" OWNER TO deviceflow_user;

--
-- Name: DeviceModel_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."DeviceModel_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."DeviceModel_id_seq" OWNER TO deviceflow_user;

--
-- Name: DeviceModel_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."DeviceModel_id_seq" OWNED BY public."DeviceModel".id;


--
-- Name: MaintenanceOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."MaintenanceOrder" (
    id integer NOT NULL,
    "orderNumber" text NOT NULL,
    "referenceNumber" text,
    date text NOT NULL,
    "employeeName" text NOT NULL,
    "maintenanceEmployeeId" integer,
    "maintenanceEmployeeName" text,
    items jsonb NOT NULL,
    notes text,
    "attachmentName" text,
    status text DEFAULT 'wip'::text NOT NULL,
    source text DEFAULT 'warehouse'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."MaintenanceOrder" OWNER TO deviceflow_user;

--
-- Name: MaintenanceOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."MaintenanceOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."MaintenanceOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: MaintenanceOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."MaintenanceOrder_id_seq" OWNED BY public."MaintenanceOrder".id;


--
-- Name: MaintenanceReceiptOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."MaintenanceReceiptOrder" (
    id integer NOT NULL,
    "receiptNumber" text NOT NULL,
    "referenceNumber" text,
    date text NOT NULL,
    "employeeName" text NOT NULL,
    "maintenanceEmployeeName" text,
    items jsonb NOT NULL,
    notes text,
    "attachmentName" text,
    status text DEFAULT 'completed'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."MaintenanceReceiptOrder" OWNER TO deviceflow_user;

--
-- Name: MaintenanceReceiptOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."MaintenanceReceiptOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."MaintenanceReceiptOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: MaintenanceReceiptOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."MaintenanceReceiptOrder_id_seq" OWNED BY public."MaintenanceReceiptOrder".id;


--
-- Name: Post; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Post" (
    id integer NOT NULL,
    title text NOT NULL,
    content text,
    published boolean DEFAULT false NOT NULL,
    "authorId" integer NOT NULL
);


ALTER TABLE public."Post" OWNER TO deviceflow_user;

--
-- Name: Post_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Post_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Post_id_seq" OWNER TO deviceflow_user;

--
-- Name: Post_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Post_id_seq" OWNED BY public."Post".id;


--
-- Name: Return; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Return" (
    id integer NOT NULL,
    "roNumber" text NOT NULL,
    "opReturnNumber" text NOT NULL,
    date text NOT NULL,
    "saleId" integer NOT NULL,
    "soNumber" text NOT NULL,
    "clientName" text NOT NULL,
    "warehouseName" text NOT NULL,
    items jsonb NOT NULL,
    notes text,
    status text DEFAULT 'معلق'::text NOT NULL,
    "processedBy" text,
    "processedDate" text,
    "employeeName" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    attachments text
);


ALTER TABLE public."Return" OWNER TO deviceflow_user;

--
-- Name: Return_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Return_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Return_id_seq" OWNER TO deviceflow_user;

--
-- Name: Return_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Return_id_seq" OWNED BY public."Return".id;


--
-- Name: Sale; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Sale" (
    id integer NOT NULL,
    "soNumber" text NOT NULL,
    "opNumber" text NOT NULL,
    date text NOT NULL,
    "clientName" text NOT NULL,
    "warehouseName" text NOT NULL,
    items jsonb NOT NULL,
    notes text,
    "warrantyPeriod" text NOT NULL,
    "employeeName" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    attachments text
);


ALTER TABLE public."Sale" OWNER TO deviceflow_user;

--
-- Name: Sale_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Sale_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Sale_id_seq" OWNER TO deviceflow_user;

--
-- Name: Sale_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Sale_id_seq" OWNED BY public."Sale".id;


--
-- Name: Supplier; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Supplier" (
    id integer NOT NULL,
    name text NOT NULL,
    phone text,
    email text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Supplier" OWNER TO deviceflow_user;

--
-- Name: Supplier_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Supplier_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Supplier_id_seq" OWNER TO deviceflow_user;

--
-- Name: Supplier_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Supplier_id_seq" OWNED BY public."Supplier".id;


--
-- Name: SupplyOrder; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."SupplyOrder" (
    id integer NOT NULL,
    "supplyOrderId" text NOT NULL,
    "supplierId" integer NOT NULL,
    "invoiceNumber" text,
    "supplyDate" text NOT NULL,
    "warehouseId" integer NOT NULL,
    "employeeName" text NOT NULL,
    items jsonb NOT NULL,
    notes text,
    "invoiceFileName" text,
    "referenceNumber" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    status text DEFAULT 'completed'::text
);


ALTER TABLE public."SupplyOrder" OWNER TO deviceflow_user;

--
-- Name: SupplyOrder_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."SupplyOrder_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."SupplyOrder_id_seq" OWNER TO deviceflow_user;

--
-- Name: SupplyOrder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."SupplyOrder_id_seq" OWNED BY public."SupplyOrder".id;


--
-- Name: SystemSetting; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."SystemSetting" (
    id integer DEFAULT 1 NOT NULL,
    "logoUrl" text DEFAULT ''::text NOT NULL,
    "companyNameAr" text DEFAULT ''::text NOT NULL,
    "companyNameEn" text DEFAULT ''::text NOT NULL,
    "addressAr" text DEFAULT ''::text NOT NULL,
    "addressEn" text DEFAULT ''::text NOT NULL,
    phone text DEFAULT ''::text NOT NULL,
    email text DEFAULT ''::text NOT NULL,
    website text DEFAULT ''::text NOT NULL,
    "footerTextAr" text DEFAULT ''::text NOT NULL,
    "footerTextEn" text DEFAULT ''::text NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."SystemSetting" OWNER TO deviceflow_user;

--
-- Name: Warehouse; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public."Warehouse" (
    id integer NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    location text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."Warehouse" OWNER TO deviceflow_user;

--
-- Name: Warehouse_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public."Warehouse_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."Warehouse_id_seq" OWNER TO deviceflow_user;

--
-- Name: Warehouse_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public."Warehouse_id_seq" OWNED BY public."Warehouse".id;


--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO deviceflow_user;

--
-- Name: database_backups; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.database_backups (
    id integer NOT NULL,
    name text NOT NULL,
    description text,
    "filePath" text NOT NULL,
    "fileSize" text NOT NULL,
    "backupType" text DEFAULT 'manual'::text NOT NULL,
    status text DEFAULT 'completed'::text NOT NULL,
    "createdBy" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "connectionId" integer NOT NULL
);


ALTER TABLE public.database_backups OWNER TO deviceflow_user;

--
-- Name: database_backups_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.database_backups_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.database_backups_id_seq OWNER TO deviceflow_user;

--
-- Name: database_backups_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.database_backups_id_seq OWNED BY public.database_backups.id;


--
-- Name: database_connections; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.database_connections (
    id integer NOT NULL,
    name text NOT NULL,
    host text NOT NULL,
    port integer DEFAULT 5432 NOT NULL,
    database text NOT NULL,
    username text NOT NULL,
    password text NOT NULL,
    "isActive" boolean DEFAULT false NOT NULL,
    "isDefault" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.database_connections OWNER TO deviceflow_user;

--
-- Name: database_connections_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.database_connections_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.database_connections_id_seq OWNER TO deviceflow_user;

--
-- Name: database_connections_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.database_connections_id_seq OWNED BY public.database_connections.id;


--
-- Name: databases; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.databases (
    id integer NOT NULL,
    name text NOT NULL,
    "connectionId" integer NOT NULL,
    owner text DEFAULT ''::text NOT NULL,
    template text DEFAULT 'template0'::text NOT NULL,
    encoding text DEFAULT 'UTF8'::text NOT NULL,
    "createdBy" integer NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.databases OWNER TO deviceflow_user;

--
-- Name: databases_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.databases_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.databases_id_seq OWNER TO deviceflow_user;

--
-- Name: databases_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.databases_id_seq OWNED BY public.databases.id;


--
-- Name: employee_requests; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.employee_requests (
    id integer NOT NULL,
    "requestNumber" text NOT NULL,
    "requestType" text NOT NULL,
    priority text NOT NULL,
    notes text NOT NULL,
    status text DEFAULT 'قيد المراجعة'::text NOT NULL,
    "requestDate" text NOT NULL,
    "employeeName" text NOT NULL,
    "employeeId" integer NOT NULL,
    "relatedOrderType" text,
    "relatedOrderId" integer,
    "relatedOrderDisplayId" text,
    "attachmentName" text,
    "adminNotes" text,
    "processedBy" integer,
    "processedDate" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.employee_requests OWNER TO deviceflow_user;

--
-- Name: employee_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.employee_requests_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.employee_requests_id_seq OWNER TO deviceflow_user;

--
-- Name: employee_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.employee_requests_id_seq OWNED BY public.employee_requests.id;


--
-- Name: evaluation_orders; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.evaluation_orders (
    id integer NOT NULL,
    "orderId" text NOT NULL,
    "employeeName" text NOT NULL,
    date text NOT NULL,
    items jsonb NOT NULL,
    notes text,
    status text DEFAULT 'معلق'::text NOT NULL,
    "acknowledgedBy" text,
    "acknowledgedDate" text,
    "warehouseName" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.evaluation_orders OWNER TO deviceflow_user;

--
-- Name: evaluation_orders_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.evaluation_orders_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.evaluation_orders_id_seq OWNER TO deviceflow_user;

--
-- Name: evaluation_orders_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.evaluation_orders_id_seq OWNED BY public.evaluation_orders.id;


--
-- Name: internal_messages; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.internal_messages (
    id integer NOT NULL,
    "threadId" integer NOT NULL,
    "senderId" integer NOT NULL,
    "senderName" text NOT NULL,
    "recipientId" integer NOT NULL,
    "recipientName" text NOT NULL,
    "recipientIds" jsonb,
    text text NOT NULL,
    "attachmentName" text,
    "attachmentContent" text,
    "attachmentType" text,
    "attachmentUrl" text,
    "attachmentFileName" text,
    "attachmentSize" integer,
    "sentDate" text NOT NULL,
    status text DEFAULT 'مرسلة'::text NOT NULL,
    "isRead" boolean DEFAULT false NOT NULL,
    "parentMessageId" integer,
    "employeeRequestId" integer,
    "resolutionNote" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.internal_messages OWNER TO deviceflow_user;

--
-- Name: internal_messages_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.internal_messages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.internal_messages_id_seq OWNER TO deviceflow_user;

--
-- Name: internal_messages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.internal_messages_id_seq OWNED BY public.internal_messages.id;


--
-- Name: maintenance_logs; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.maintenance_logs (
    id integer NOT NULL,
    "deviceId" text NOT NULL,
    model text NOT NULL,
    "repairDate" text NOT NULL,
    notes text,
    result text,
    status text DEFAULT 'pending'::text NOT NULL,
    "acknowledgedDate" text,
    "warehouseName" text,
    "acknowledgedBy" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.maintenance_logs OWNER TO deviceflow_user;

--
-- Name: maintenance_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.maintenance_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.maintenance_logs_id_seq OWNER TO deviceflow_user;

--
-- Name: maintenance_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.maintenance_logs_id_seq OWNED BY public.maintenance_logs.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: deviceflow_user
--

CREATE TABLE public.users (
    id integer NOT NULL,
    email text NOT NULL,
    name text,
    username text DEFAULT 'user'::text,
    role text DEFAULT 'user'::text,
    phone text DEFAULT ''::text,
    photo text DEFAULT ''::text,
    status text DEFAULT 'Active'::text,
    "lastLogin" text,
    "branchLocation" text,
    "warehouseAccess" jsonb,
    permissions jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.users OWNER TO deviceflow_user;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: deviceflow_user
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO deviceflow_user;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: deviceflow_user
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: AuditLog id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."AuditLog" ALTER COLUMN id SET DEFAULT nextval('public."AuditLog_id_seq"'::regclass);


--
-- Name: Client id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Client" ALTER COLUMN id SET DEFAULT nextval('public."Client_id_seq"'::regclass);


--
-- Name: DeliveryOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeliveryOrder" ALTER COLUMN id SET DEFAULT nextval('public."DeliveryOrder_id_seq"'::regclass);


--
-- Name: DeviceModel id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeviceModel" ALTER COLUMN id SET DEFAULT nextval('public."DeviceModel_id_seq"'::regclass);


--
-- Name: MaintenanceOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceOrder" ALTER COLUMN id SET DEFAULT nextval('public."MaintenanceOrder_id_seq"'::regclass);


--
-- Name: MaintenanceReceiptOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceReceiptOrder" ALTER COLUMN id SET DEFAULT nextval('public."MaintenanceReceiptOrder_id_seq"'::regclass);


--
-- Name: Post id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Post" ALTER COLUMN id SET DEFAULT nextval('public."Post_id_seq"'::regclass);


--
-- Name: Return id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Return" ALTER COLUMN id SET DEFAULT nextval('public."Return_id_seq"'::regclass);


--
-- Name: Sale id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Sale" ALTER COLUMN id SET DEFAULT nextval('public."Sale_id_seq"'::regclass);


--
-- Name: Supplier id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Supplier" ALTER COLUMN id SET DEFAULT nextval('public."Supplier_id_seq"'::regclass);


--
-- Name: SupplyOrder id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."SupplyOrder" ALTER COLUMN id SET DEFAULT nextval('public."SupplyOrder_id_seq"'::regclass);


--
-- Name: Warehouse id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Warehouse" ALTER COLUMN id SET DEFAULT nextval('public."Warehouse_id_seq"'::regclass);


--
-- Name: database_backups id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_backups ALTER COLUMN id SET DEFAULT nextval('public.database_backups_id_seq'::regclass);


--
-- Name: database_connections id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_connections ALTER COLUMN id SET DEFAULT nextval('public.database_connections_id_seq'::regclass);


--
-- Name: databases id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.databases ALTER COLUMN id SET DEFAULT nextval('public.databases_id_seq'::regclass);


--
-- Name: employee_requests id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.employee_requests ALTER COLUMN id SET DEFAULT nextval('public.employee_requests_id_seq'::regclass);


--
-- Name: evaluation_orders id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.evaluation_orders ALTER COLUMN id SET DEFAULT nextval('public.evaluation_orders_id_seq'::regclass);


--
-- Name: internal_messages id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.internal_messages ALTER COLUMN id SET DEFAULT nextval('public.internal_messages_id_seq'::regclass);


--
-- Name: maintenance_logs id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_logs ALTER COLUMN id SET DEFAULT nextval('public.maintenance_logs_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: AuditLog; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."AuditLog" (id, "timestamp", "userId", username, operation, details) FROM stdin;
1	2025-07-25 18:51:33.989	1	admin	CREATE	Created warehouse: omar albkri
2	2025-07-25 18:52:03.156	1	admin	CREATE	Created supplier: عمر
3	2025-07-25 18:52:44.445	1	admin	CREATE	Created supply order: SUP-1
4	2025-07-25 18:54:15.12	1	admin	CREATE	Created evaluation order: EVAL-20250725-001
5	2025-07-25 19:02:53.719	1	admin	CREATE	Created user: omar albkri (<EMAIL>)
6	2025-07-25 19:08:19.531	1	admin	CREATE	Created maintenance order: MAINT-1
7	2025-07-25 19:12:13.881	1	admin	CREATE	Created client: البكري
8	2025-07-25 19:13:35.802	1	admin	CREATE	Created sale: SO-1753470815784786 for client البكري
9	2025-07-25 19:21:30.237	1	admin	DELETE	Deleted sale: SO-1753470815784786 for client البكري
10	2025-07-25 19:27:27.062	1	admin	CREATE	Created supply order: SUP-2
11	2025-07-25 19:28:34.423	1	admin	CREATE	Created sale: SO-1753471714383150 for client البكري
12	2025-07-25 20:14:23.029	1	admin	UPLOAD	Uploaded 1 files to section: maintenance
13	2025-07-25 20:45:22.775	1	admin	UPLOAD	Uploaded 1 files to section: maintenance
14	2025-07-25 22:44:50.762	1	admin	CREATE	Created maintenance order: MAINT-2
15	2025-07-25 23:01:56.733	1	admin	CREATE	Created maintenance order: MAINT-3
16	2025-07-25 23:02:44.228	1	admin	CREATE	Created maintenance order: MAINT-4
17	2025-07-25 23:02:44.554	1	admin	CREATE	Created maintenance order: MAINT-5
18	2025-07-25 23:03:51.419	1	admin	CREATE	Created maintenance order: MAINT-6
19	2025-07-25 23:17:29.623	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
20	2025-07-25 23:17:34.54	1	admin	CREATE	Created maintenance order: MAINT-7
21	2025-07-25 23:17:34.933	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
22	2025-07-25 23:48:42.715	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
23	2025-07-25 23:48:53.767	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
24	2025-07-25 23:49:33.099	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
25	2025-07-25 23:49:42.576	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
26	2025-07-25 23:49:54.339	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
27	2025-07-25 23:50:02.521	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
28	2025-07-25 23:56:58.834	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
29	2025-07-25 23:57:08.51	1	admin	CREATE	Created maintenance order: MAINT-2
30	2025-07-25 23:57:08.756	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
31	2025-07-25 23:57:23.905	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
32	2025-07-25 23:57:37.579	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
33	2025-07-25 23:57:39.72	1	admin	CREATE	Created maintenance order: MAINT-2
34	2025-07-25 23:57:39.867	1	admin	CREATE	Created maintenance order: MAINT-3
35	2025-07-25 23:57:39.97	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
36	2025-07-25 23:57:40.803	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
37	2025-07-25 23:58:02.228	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
38	2025-07-26 20:47:18.141	1	admin	CREATE	Created return: RO-1 for client البكري
39	2025-07-26 20:47:18.639	1	admin	UPDATE	Updated device: 222222222222222 - Apple 22 128GB
40	2025-07-26 21:51:52.242	1	admin	CREATE	Created evaluation order: EVAL-1
41	2025-07-26 21:51:55.532	1	admin	UPDATE	Updated device: 222222222222222 - Apple 22 128GB
42	2025-07-26 22:27:24.526	1	admin	CREATE	Created evaluation order: EVAL-2
43	2025-07-26 22:27:24.988	1	admin	UPDATE	Updated device: 222222222222222 - Apple 22 128GB
44	2025-07-26 22:27:58.303	1	admin	CREATE	Created evaluation order: EVAL-3
45	2025-07-26 22:27:58.583	1	admin	UPDATE	Updated device: 111111111111111 - Apple 22 128GB
46	2025-07-26 22:47:14.735	1	admin	CREATE	Created evaluation order: EVAL-4
47	2025-07-26 22:47:24.134	1	admin	UPDATE	Updated device: 222222222222222 - Apple 22 128GB
48	2025-07-26 23:03:36.449	1	admin	CREATE	Created evaluation order: EVAL-5
49	2025-07-26 23:03:37.833	1	admin	UPDATE	Updated device: 222222222222222 - Apple 22 128GB
50	2025-07-26 23:16:04.207	1	admin	CREATE	Created supply order: SUP-3
\.


--
-- Data for Name: Client; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Client" (id, name, phone, email, "createdAt", "updatedAt") FROM stdin;
1	البكري			2025-07-25 19:12:13.872	2025-07-25 19:12:13.872
\.


--
-- Data for Name: DeliveryOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."DeliveryOrder" (id, "deliveryOrderNumber", "referenceNumber", date, "warehouseId", "warehouseName", "employeeName", items, notes, "attachmentName", status, "createdAt") FROM stdin;
\.


--
-- Data for Name: Device; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Device" (id, model, status, storage, price, condition, "warehouseId", "supplierId", "dateAdded", "replacementInfo") FROM stdin;
111111111111111	Apple 22 128GB	متاح للبيع	N/A	0	جديد	1	1	2025-07-25 19:27:27.059	null
222222222222222	Apple 22 128GB	متاح للبيع	N/A	0	جديد	1	1	2025-07-25 18:52:44.44	null
222222222222229	Apple 22 128GB	متاح للبيع	N/A	0	جديد	1	1	2025-07-26 23:16:04.205	\N
\.


--
-- Data for Name: DeviceModel; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."DeviceModel" (id, name, "manufacturerId", category, "createdAt", "updatedAt") FROM stdin;
1	22 128GB	1	هاتف ذكي	2025-07-25 18:52:36.027	2025-07-25 18:52:36.027
\.


--
-- Data for Name: MaintenanceOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."MaintenanceOrder" (id, "orderNumber", "referenceNumber", date, "employeeName", "maintenanceEmployeeId", "maintenanceEmployeeName", items, notes, "attachmentName", status, source, "createdAt") FROM stdin;
1	MAINT-1	\N	2025-07-25T16:08:00.000Z	System Administrator	\N	omar albkri	"[{\\"id\\":\\"222222222222222\\",\\"model\\":\\"Apple 22 128GB\\",\\"status\\":\\"متاح للبيع\\",\\"storage\\":\\"N/A\\",\\"price\\":0,\\"condition\\":\\"جديد\\",\\"warehouseId\\":1,\\"supplierId\\":1,\\"dateAdded\\":\\"2025-07-25T18:52:44.440Z\\",\\"replacementInfo\\":null}]"	\N	\N	wip	warehouse	2025-07-25 19:08:19.51
9	MAINT-2	\N	2025-07-25T20:57:00.000Z	System Administrator	\N	\N	"[{\\"id\\":\\"111111111111111\\",\\"model\\":\\"Apple 22 128GB\\",\\"status\\":\\"بانتظار استلام في الصيانة\\",\\"storage\\":\\"N/A\\",\\"price\\":0,\\"condition\\":\\"جديد\\",\\"warehouseId\\":1,\\"supplierId\\":1,\\"dateAdded\\":\\"2025-07-25T19:27:27.059Z\\",\\"replacementInfo\\":null}]"	\N	\N	wip	direct	2025-07-25 23:57:39.713
\.


--
-- Data for Name: MaintenanceReceiptOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."MaintenanceReceiptOrder" (id, "receiptNumber", "referenceNumber", date, "employeeName", "maintenanceEmployeeName", items, notes, "attachmentName", status, "createdAt") FROM stdin;
\.


--
-- Data for Name: Post; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Post" (id, title, content, published, "authorId") FROM stdin;
\.


--
-- Data for Name: Return; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Return" (id, "roNumber", "opReturnNumber", date, "saleId", "soNumber", "clientName", "warehouseName", items, notes, status, "processedBy", "processedDate", "employeeName", "createdAt", attachments) FROM stdin;
1	RO-1	RO-1	2025-07-26T00:00:00.000Z	2	SO-1753471714383150	البكري	omar albkri	"[{\\"deviceId\\":\\"222222222222222\\",\\"model\\":\\"Apple 22 128GB\\",\\"returnReason\\":\\"خلل مصنعي\\",\\"isReplacement\\":false}]"		مكتمل	System Administrator	2025-07-26T20:47:17.672Z	System Administrator	2025-07-26 20:47:18.074	[]
\.


--
-- Data for Name: Sale; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Sale" (id, "soNumber", "opNumber", date, "clientName", "warehouseName", items, notes, "warrantyPeriod", "employeeName", "createdAt", attachments) FROM stdin;
2	SO-1753471714383150	SO-1	2025-07-25T19:23	البكري	omar albkri	"[{\\"deviceId\\":\\"222222222222222\\",\\"model\\":\\"Apple 22 128GB\\",\\"price\\":0,\\"condition\\":\\"جديد\\"}]"		none	System Administrator	2025-07-25 19:28:34.395	[]
\.


--
-- Data for Name: Supplier; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Supplier" (id, name, phone, email, "createdAt", "updatedAt") FROM stdin;
1	عمر			2025-07-25 18:52:03.102	2025-07-25 18:52:03.102
\.


--
-- Data for Name: SupplyOrder; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."SupplyOrder" (id, "supplyOrderId", "supplierId", "invoiceNumber", "supplyDate", "warehouseId", "employeeName", items, notes, "invoiceFileName", "referenceNumber", "createdAt", status) FROM stdin;
1	SUP-1	1	\N	2025-07-25T18:51	1	System Administrator	"[{\\"imei\\":\\"222222222222222\\",\\"manufacturer\\":\\"Apple\\",\\"model\\":\\"22 128GB\\",\\"condition\\":\\"جديد\\"}]"		\N	\N	2025-07-25 18:52:44.428	completed
2	SUP-2	1	\N	2025-07-25T19:24	1	System Administrator	"[{\\"imei\\":\\"111111111111111\\",\\"manufacturer\\":\\"Apple\\",\\"model\\":\\"22 128GB\\",\\"condition\\":\\"جديد\\"}]"		\N	\N	2025-07-25 19:27:27.051	completed
3	SUP-3	1	\N	2025-07-26T23:15	1	System Administrator	"[{\\"imei\\":\\"222222222222229\\",\\"manufacturer\\":\\"Apple\\",\\"model\\":\\"22 128GB\\",\\"condition\\":\\"جديد\\"}]"		\N	\N	2025-07-26 23:16:04.191	completed
\.


--
-- Data for Name: SystemSetting; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."SystemSetting" (id, "logoUrl", "companyNameAr", "companyNameEn", "addressAr", "addressEn", phone, email, website, "footerTextAr", "footerTextEn", "updatedAt", "createdAt") FROM stdin;
\.


--
-- Data for Name: Warehouse; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public."Warehouse" (id, name, type, location, "createdAt", "updatedAt") FROM stdin;
1	omar albkri	رئيسي	yemen	2025-07-25 18:51:33.986	2025-07-25 18:51:33.986
\.


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
e32df8e5-48e5-428b-a027-650abde07b7b	3d1565b12dab38ebfaccf6608176d58057f534ea3bf8bb8b5cbfb6219053b45b	2025-07-25 18:43:53.691371+00	20250725184353_init_postgresql	\N	\N	2025-07-25 18:43:53.410362+00	1
d5f64396-8f3b-4f90-983a-3c690c2b3957	10d18ecbe580484ed29c2e2d87050144d0851559d555ec1ee1f1fb5939646486	2025-07-26 00:54:42.159711+00	20250726005442_add_employee_requests	\N	\N	2025-07-26 00:54:42.116663+00	1
5f45757e-1570-4324-be0d-f27bdfd0055d	c83f208a96a97a1a7843d1bfa936d78fd5371a81b5860e0c3542e79e255f5774	2025-07-26 18:23:46.659108+00	20250726182346_add_internal_messages	\N	\N	2025-07-26 18:23:46.631129+00	1
\.


--
-- Data for Name: database_backups; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.database_backups (id, name, description, "filePath", "fileSize", "backupType", status, "createdBy", "createdAt", "connectionId") FROM stdin;
\.


--
-- Data for Name: database_connections; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.database_connections (id, name, host, port, database, username, password, "isActive", "isDefault", "createdAt", "updatedAt") FROM stdin;
1	الاتصال الافتراضي	localhost	5432	deviceflow_db	deviceflow_user	om772828	t	t	2025-07-26 23:30:43.925	2025-07-26 23:30:43.925
\.


--
-- Data for Name: databases; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.databases (id, name, "connectionId", owner, template, encoding, "createdBy", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: employee_requests; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.employee_requests (id, "requestNumber", "requestType", priority, notes, status, "requestDate", "employeeName", "employeeId", "relatedOrderType", "relatedOrderId", "relatedOrderDisplayId", "attachmentName", "adminNotes", "processedBy", "processedDate", "createdAt", "updatedAt") FROM stdin;
1	REQ-1	تعديل	عادي	ldckdlkfdfkdsl\n	قيد المراجعة	2025-07-26T18:18:53.697Z	System Administrator	1	supply	\N	SUP-3	\N	\N	\N	\N	2025-07-26 18:18:53.706	2025-07-26 18:18:53.706
\.


--
-- Data for Name: evaluation_orders; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.evaluation_orders (id, "orderId", "employeeName", date, items, notes, status, "acknowledgedBy", "acknowledgedDate", "warehouseName", "createdAt", "updatedAt") FROM stdin;
1	EVAL-20250725-001	مدير النظام	2025-07-25T18:54:12.858Z	"[{\\"deviceId\\":\\"222222222222222\\",\\"model\\":\\"Apple 22 128GB\\",\\"externalGrade\\":\\"بدون\\",\\"screenGrade\\":\\"A+\\",\\"networkGrade\\":\\"مفتوح رسمي\\",\\"finalGrade\\":\\"جاهز للبيع\\"}]"	\N	معلق	\N	\N	\N	2025-07-25 18:54:15.117	2025-07-25 18:54:15.117
2	EVAL-1	System Administrator	2025-07-26T21:51:42.897Z	"[{\\"deviceId\\":\\"222222222222222\\",\\"model\\":\\"Apple 22 128GB\\",\\"externalGrade\\":\\"بدون\\",\\"screenGrade\\":\\"A+\\",\\"networkGrade\\":\\"مفتوح رسمي\\",\\"finalGrade\\":\\"جاهز للبيع\\"}]"	\N	معلق	\N	\N	\N	2025-07-26 21:51:52.166	2025-07-26 21:51:52.166
3	EVAL-2	System Administrator	2025-07-26T22:27:23.360Z	"[{\\"deviceId\\":\\"222222222222222\\",\\"model\\":\\"Apple 22 128GB\\",\\"externalGrade\\":\\"بدون\\",\\"screenGrade\\":\\"بدون\\",\\"networkGrade\\":\\"مفتوح رسمي\\",\\"finalGrade\\":\\"جاهز للبيع\\"}]"	\N	معلق	\N	\N	\N	2025-07-26 22:27:24.487	2025-07-26 22:27:24.487
4	EVAL-3	System Administrator	2025-07-26T22:27:57.655Z	"[{\\"deviceId\\":\\"111111111111111\\",\\"model\\":\\"Apple 22 128GB\\",\\"externalGrade\\":\\"بدون\\",\\"screenGrade\\":\\"بدون\\",\\"networkGrade\\":\\"مفتوح رسمي\\",\\"finalGrade\\":\\"جاهز للبيع\\"}]"	\N	معلق	\N	\N	\N	2025-07-26 22:27:58.307	2025-07-26 22:27:58.307
5	EVAL-4	System Administrator	2025-07-26T22:47:04.998Z	"[{\\"deviceId\\":\\"222222222222222\\",\\"model\\":\\"Apple 22 128GB\\",\\"externalGrade\\":\\"بدون\\",\\"screenGrade\\":\\"بدون\\",\\"networkGrade\\":\\"مفتوح رسمي\\",\\"finalGrade\\":\\"جاهز للبيع\\"}]"	\N	معلق	\N	\N	\N	2025-07-26 22:47:14.718	2025-07-26 22:47:14.718
7	EVAL-5	System Administrator	2025-07-26T23:03:35.154Z	"[{\\"deviceId\\":\\"222222222222222\\",\\"model\\":\\"Apple 22 128GB\\",\\"externalGrade\\":\\"بدون\\",\\"screenGrade\\":\\"بدون\\",\\"networkGrade\\":\\"مفتوح رسمي\\",\\"finalGrade\\":\\"جاهز للبيع\\"}]"	\N	معلق	\N	\N	\N	2025-07-26 23:03:36.373	2025-07-26 23:03:36.373
\.


--
-- Data for Name: internal_messages; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.internal_messages (id, "threadId", "senderId", "senderName", "recipientId", "recipientName", "recipientIds", text, "attachmentName", "attachmentContent", "attachmentType", "attachmentUrl", "attachmentFileName", "attachmentSize", "sentDate", status, "isRead", "parentMessageId", "employeeRequestId", "resolutionNote", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: maintenance_logs; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.maintenance_logs (id, "deviceId", model, "repairDate", notes, result, status, "acknowledgedDate", "warehouseName", "acknowledgedBy", "createdAt") FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: deviceflow_user
--

COPY public.users (id, email, name, username, role, phone, photo, status, "lastLogin", "branchLocation", "warehouseAccess", permissions, "createdAt", "updatedAt") FROM stdin;
1	<EMAIL>	System Administrator	admin	admin			Active	\N	\N	\N	\N	2025-07-25 18:48:49.939	2025-07-25 18:48:49.939
2	<EMAIL>	omar albkri	albakrepc2	صيانة	0772828207		Active	\N	\N	null	"{\\"dashboard\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"track\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"supply\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"acceptDevices\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"grading\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"inventory\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"sales\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"maintenance\\":{\\"view\\":true,\\"create\\":true,\\"edit\\":true,\\"delete\\":false},\\"maintenanceTransfer\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"warehouseTransfer\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"clients\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"pricing\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"returns\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"warehouses\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"users\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"reports\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"stocktaking\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"settings\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"requests\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false},\\"messaging\\":{\\"view\\":false,\\"create\\":false,\\"edit\\":false,\\"delete\\":false}}"	2025-07-25 19:02:53.714	2025-07-25 19:02:53.714
\.


--
-- Name: AuditLog_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."AuditLog_id_seq"', 50, true);


--
-- Name: Client_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Client_id_seq"', 1, true);


--
-- Name: DeliveryOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."DeliveryOrder_id_seq"', 1, false);


--
-- Name: DeviceModel_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."DeviceModel_id_seq"', 1, true);


--
-- Name: MaintenanceOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."MaintenanceOrder_id_seq"', 10, true);


--
-- Name: MaintenanceReceiptOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."MaintenanceReceiptOrder_id_seq"', 1, false);


--
-- Name: Post_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Post_id_seq"', 1, false);


--
-- Name: Return_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Return_id_seq"', 1, true);


--
-- Name: Sale_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Sale_id_seq"', 2, true);


--
-- Name: Supplier_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Supplier_id_seq"', 1, true);


--
-- Name: SupplyOrder_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."SupplyOrder_id_seq"', 3, true);


--
-- Name: Warehouse_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public."Warehouse_id_seq"', 1, true);


--
-- Name: database_backups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.database_backups_id_seq', 1, true);


--
-- Name: database_connections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.database_connections_id_seq', 1, true);


--
-- Name: databases_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.databases_id_seq', 1, false);


--
-- Name: employee_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.employee_requests_id_seq', 1, true);


--
-- Name: evaluation_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.evaluation_orders_id_seq', 7, true);


--
-- Name: internal_messages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.internal_messages_id_seq', 1, false);


--
-- Name: maintenance_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.maintenance_logs_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: deviceflow_user
--

SELECT pg_catalog.setval('public.users_id_seq', 2, true);


--
-- Name: AuditLog AuditLog_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."AuditLog"
    ADD CONSTRAINT "AuditLog_pkey" PRIMARY KEY (id);


--
-- Name: Client Client_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Client"
    ADD CONSTRAINT "Client_pkey" PRIMARY KEY (id);


--
-- Name: DeliveryOrder DeliveryOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeliveryOrder"
    ADD CONSTRAINT "DeliveryOrder_pkey" PRIMARY KEY (id);


--
-- Name: DeviceModel DeviceModel_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."DeviceModel"
    ADD CONSTRAINT "DeviceModel_pkey" PRIMARY KEY (id);


--
-- Name: Device Device_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Device"
    ADD CONSTRAINT "Device_pkey" PRIMARY KEY (id);


--
-- Name: MaintenanceOrder MaintenanceOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceOrder"
    ADD CONSTRAINT "MaintenanceOrder_pkey" PRIMARY KEY (id);


--
-- Name: MaintenanceReceiptOrder MaintenanceReceiptOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."MaintenanceReceiptOrder"
    ADD CONSTRAINT "MaintenanceReceiptOrder_pkey" PRIMARY KEY (id);


--
-- Name: Post Post_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Post"
    ADD CONSTRAINT "Post_pkey" PRIMARY KEY (id);


--
-- Name: Return Return_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Return"
    ADD CONSTRAINT "Return_pkey" PRIMARY KEY (id);


--
-- Name: Sale Sale_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Sale"
    ADD CONSTRAINT "Sale_pkey" PRIMARY KEY (id);


--
-- Name: Supplier Supplier_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Supplier"
    ADD CONSTRAINT "Supplier_pkey" PRIMARY KEY (id);


--
-- Name: SupplyOrder SupplyOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."SupplyOrder"
    ADD CONSTRAINT "SupplyOrder_pkey" PRIMARY KEY (id);


--
-- Name: SystemSetting SystemSetting_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."SystemSetting"
    ADD CONSTRAINT "SystemSetting_pkey" PRIMARY KEY (id);


--
-- Name: Warehouse Warehouse_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Warehouse"
    ADD CONSTRAINT "Warehouse_pkey" PRIMARY KEY (id);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: database_backups database_backups_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_backups
    ADD CONSTRAINT database_backups_pkey PRIMARY KEY (id);


--
-- Name: database_connections database_connections_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_connections
    ADD CONSTRAINT database_connections_pkey PRIMARY KEY (id);


--
-- Name: databases databases_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.databases
    ADD CONSTRAINT databases_pkey PRIMARY KEY (id);


--
-- Name: employee_requests employee_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.employee_requests
    ADD CONSTRAINT employee_requests_pkey PRIMARY KEY (id);


--
-- Name: evaluation_orders evaluation_orders_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.evaluation_orders
    ADD CONSTRAINT evaluation_orders_pkey PRIMARY KEY (id);


--
-- Name: internal_messages internal_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.internal_messages
    ADD CONSTRAINT internal_messages_pkey PRIMARY KEY (id);


--
-- Name: maintenance_logs maintenance_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.maintenance_logs
    ADD CONSTRAINT maintenance_logs_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: DeliveryOrder_deliveryOrderNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "DeliveryOrder_deliveryOrderNumber_key" ON public."DeliveryOrder" USING btree ("deliveryOrderNumber");


--
-- Name: DeviceModel_name_manufacturerId_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "DeviceModel_name_manufacturerId_key" ON public."DeviceModel" USING btree (name, "manufacturerId");


--
-- Name: MaintenanceOrder_orderNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "MaintenanceOrder_orderNumber_key" ON public."MaintenanceOrder" USING btree ("orderNumber");


--
-- Name: MaintenanceReceiptOrder_receiptNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "MaintenanceReceiptOrder_receiptNumber_key" ON public."MaintenanceReceiptOrder" USING btree ("receiptNumber");


--
-- Name: Return_roNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "Return_roNumber_key" ON public."Return" USING btree ("roNumber");


--
-- Name: Sale_soNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "Sale_soNumber_key" ON public."Sale" USING btree ("soNumber");


--
-- Name: SupplyOrder_supplyOrderId_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "SupplyOrder_supplyOrderId_key" ON public."SupplyOrder" USING btree ("supplyOrderId");


--
-- Name: database_connections_name_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX database_connections_name_key ON public.database_connections USING btree (name);


--
-- Name: databases_name_connectionId_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "databases_name_connectionId_key" ON public.databases USING btree (name, "connectionId");


--
-- Name: employee_requests_requestNumber_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "employee_requests_requestNumber_key" ON public.employee_requests USING btree ("requestNumber");


--
-- Name: evaluation_orders_orderId_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX "evaluation_orders_orderId_key" ON public.evaluation_orders USING btree ("orderId");


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: users_username_key; Type: INDEX; Schema: public; Owner: deviceflow_user
--

CREATE UNIQUE INDEX users_username_key ON public.users USING btree (username);


--
-- Name: Post Post_authorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public."Post"
    ADD CONSTRAINT "Post_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: database_backups database_backups_connectionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.database_backups
    ADD CONSTRAINT "database_backups_connectionId_fkey" FOREIGN KEY ("connectionId") REFERENCES public.database_connections(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: databases databases_connectionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.databases
    ADD CONSTRAINT "databases_connectionId_fkey" FOREIGN KEY ("connectionId") REFERENCES public.database_connections(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: databases databases_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: deviceflow_user
--

ALTER TABLE ONLY public.databases
    ADD CONSTRAINT "databases_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT ALL ON SCHEMA public TO deviceflow_user;


--
-- PostgreSQL database dump complete
--

