const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkConnections() {
  try {
    console.log('🔍 فحص اتصالات قواعد البيانات...\n');
    
    const connections = await prisma.databaseConnection.findMany();
    
    if (connections.length === 0) {
      console.log('❌ لا توجد اتصالات في قاعدة البيانات');
      return;
    }
    
    console.log(`📊 تم العثور على ${connections.length} اتصال:\n`);
    
    connections.forEach((conn, index) => {
      console.log(`${index + 1}. الاسم: ${conn.name}`);
      console.log(`   المضيف: ${conn.host}:${conn.port}`);
      console.log(`   قاعدة البيانات: ${conn.database}`);
      console.log(`   نشط: ${conn.isActive ? '✅ نعم' : '❌ لا'}`);
      console.log(`   افتراضي: ${conn.isDefault ? '✅ نعم' : '❌ لا'}`);
      console.log(`   تم الإنشاء: ${conn.createdAt.toLocaleString('ar-SA')}`);
      console.log('   ───────────────────────────────');
    });
    
    const activeCount = connections.filter(c => c.isActive).length;
    console.log(`\n📈 الإحصائيات:`);
    console.log(`   - إجمالي الاتصالات: ${connections.length}`);
    console.log(`   - الاتصالات النشطة: ${activeCount}`);
    console.log(`   - الاتصالات غير النشطة: ${connections.length - activeCount}`);
    
  } catch (error) {
    console.error('❌ خطأ في فحص الاتصالات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkConnections();
