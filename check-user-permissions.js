const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUserPermissions() {
  try {
    console.log('🔍 فحص صلاحيات المستخدمين المديرين...');
    
    const users = await prisma.user.findMany({
      where: { role: 'admin' },
      select: {
        id: true,
        name: true,
        username: true,
        email: true,
        role: true,
        permissions: true
      }
    });
    
    console.log(`📊 تم العثور على ${users.length} مدير:`);
    users.forEach(user => {
      console.log(`\n👤 المستخدم: ${user.name}`);
      console.log(`🔑 اسم المستخدم: ${user.username}`);
      console.log(`📧 البريد: ${user.email}`);
      console.log(`📋 نوع الصلاحيات: ${typeof user.permissions}`);
      
      if (user.permissions) {
        if (typeof user.permissions === 'string') {
          try {
            const parsed = JSON.parse(user.permissions);
            console.log('✅ الصلاحيات (parsed):');
            console.log(JSON.stringify(parsed, null, 2));
          } catch (e) {
            console.log('❌ فشل في parse الصلاحيات');
          }
        } else {
          console.log('✅ الصلاحيات (object):');
          console.log(JSON.stringify(user.permissions, null, 2));
        }
      } else {
        console.log('⚠️ لا توجد صلاحيات');
      }
      console.log('───────────────────────────────');
    });
    
  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUserPermissions();
