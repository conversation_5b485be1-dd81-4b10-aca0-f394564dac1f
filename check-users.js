const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log('🔍 فحص المستخدمين في النظام...\n');
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        status: true,
        createdAt: true
      },
      orderBy: { createdAt: 'asc' }
    });
    
    if (users.length === 0) {
      console.log('❌ لا يوجد مستخدمين في النظام');
      return;
    }
    
    console.log(`📊 تم العثور على ${users.length} مستخدم:\n`);
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. المعرف: ${user.id}`);
      console.log(`   اسم المستخدم: ${user.username}`);
      console.log(`   البريد: ${user.email || 'غير محدد'}`);
      console.log(`   الدور: ${user.role}`);
      console.log(`   نشط: ${user.status === 'Active' ? '✅ نعم' : '❌ لا'}`);
      console.log(`   تم الإنشاء: ${user.createdAt.toLocaleString('ar-SA')}`);
      console.log('   ───────────────────────────────');
    });
    
    const adminUsers = users.filter(u => u.role === 'admin');
    const maintenanceUsers = users.filter(u => u.role === 'maintenance');
    
    console.log(`\n📈 الإحصائيات:`);
    console.log(`   - إجمالي المستخدمين: ${users.length}`);
    console.log(`   - مديرين: ${adminUsers.length}`);
    console.log(`   - صيانة: ${maintenanceUsers.length}`);
    console.log(`   - آخرين: ${users.length - adminUsers.length - maintenanceUsers.length}`);
    
  } catch (error) {
    console.error('❌ خطأ في فحص المستخدمين:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
