import { hasPermission } from '../lib/auth';

export default function canView(currentUser: any, pageKey: string, action: string = 'view') {
  if (!currentUser) {
    console.warn(`Can't check permissions for ${pageKey} currentUser missing`);
    return false;
  }
  
  if (!currentUser.permissions) {
    console.warn(`Can't check permissions for ${pageKey} permissions missing`);
    return false;
  }
  
  // اعتبر أن المستخدم المدير له جميع الصلاحيات
  if (currentUser.role === 'admin' || currentUser.role === 'إدارة') {
    return true;
  }
  
  return hasPermission(currentUser.permissions, pageKey, action);
}