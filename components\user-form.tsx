'use client';

import { useState, useEffect, useRef } from 'react';
import { User, AppPermissions, permissionPages } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { PermissionsSection } from './permissions-section';

interface UserFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (user: User, password?: string) => void;
  currentUser?: User;
}

const initialPermissions: AppPermissions = permissionPages.reduce(
  (acc, page) => ({
    ...acc,
    [page]: { view: false, create: false, edit: false, delete: false },
  }),
  {} as AppPermissions,
);

const permissionTranslations: Record<keyof AppPermissions, string> = {
  dashboard: 'لوحة التحكم',
  supply: 'التوريد',
  grading: 'الفحص والتقييم',
  inventory: 'المخزون',
  sales: 'المبيعات',
  returns: 'المرتجعات',
  maintenance: 'الصيانة',
  warehouseTransfer: 'النقل المخزني',
  stocktaking: 'جرد المخزون',
  reports: 'التقارير',
  users: 'المستخدمين',
  clients: 'العملاء',
  warehouses: 'إدارة المخازن',
  settings: 'الإعدادات',
  messaging: 'الرسائل',
  requests: 'الطلبات',
  acceptDevices: 'قبول الأجهزة',
  maintenanceTransfer: 'نقل الصيانة',
  track: 'التتبع',
  pricing: 'التسعير',
};

export function UserForm({
  isOpen,
  onClose,
  onSave,
  currentUser,
}: UserFormProps) {
  const { toast } = useToast();
  const [name, setName] = useState('');
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [photo, setPhoto] = useState<string | undefined>(undefined);
  const [photoPreview, setPhotoPreview] = useState<string | undefined>(
    undefined,
  );
  const [role, setRole] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [permissions, setPermissions] =
    useState<AppPermissions>(initialPermissions);
  const photoInputRef = useRef<HTMLInputElement>(null);

  const [phone, setPhone] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');



  // تحديث البيانات عند فتح النافذة أو تغيير المستخدم
  useEffect(() => {
    if (!isOpen) {
      return;
    }

    if (currentUser) {
      // تحديث البيانات الأساسية
      setName(currentUser.name);
      setUsername(currentUser.username);
      setEmail(currentUser.email);
      setPhone(currentUser.phone || '');
      setPhoto(currentUser.photo);
      setPhotoPreview(currentUser.photo);
      setRole(currentUser.role || '');
      setPassword('');
      setConfirmPassword('');
      setActiveTab('basic');
      setErrors({});
      setIsLoading(false);
    } else {
      // إعادة تعيين للمستخدم الجديد
      setName('');
      setUsername('');
      setEmail('');
      setPhone('');
      setPassword('');
      setConfirmPassword('');
      setPhoto(undefined);
      setPhotoPreview(undefined);
      setRole('');
      setActiveTab('basic');
      setErrors({});
      setIsLoading(false);
    }
  }, [currentUser?.id, isOpen]);

  // تحديث الصلاحيات في useEffect منفصل
  useEffect(() => {
    if (!isOpen || !currentUser) {
      if (!currentUser) {
        setPermissions({ ...initialPermissions });
      }
      return;
    }

    // تأخير لضمان تحديث البيانات الأساسية أولاً
    const timer = setTimeout(() => {
      // نسخ الصلاحيات مع التحقق من وجودها
      let userPermissions = initialPermissions;
      if (currentUser.permissions && typeof currentUser.permissions === 'object') {
        try {
          userPermissions = JSON.parse(JSON.stringify(currentUser.permissions));
        } catch (error) {
          console.warn('خطأ في تحليل صلاحيات المستخدم:', error);
          userPermissions = initialPermissions;
        }
      }
      setPermissions(userPermissions);
    }, 200);

    return () => clearTimeout(timer);
  }, [currentUser?.id, isOpen]);





  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onloadend = () => {
        setPhotoPreview(reader.result as string);
        setPhoto(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = 'الاسم مطلوب';
    }
    if (!username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب';
    }
    // البريد الإلكتروني اختياري، لكن إذا تم إدخاله يجب أن يكون صحيحاً
    if (email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }
    if (!currentUser && !password) {
      newErrors.password = 'كلمة المرور مطلوبة للمستخدمين الجدد';
    }
    if (password && password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'كلمات المرور غير متطابقة';
    }
    // التحقق من وجود صلاحيات
    const hasAnyPermissions = permissions && typeof permissions === 'object' &&
      Object.keys(permissions).some(key => {
        const perm = permissions[key as keyof AppPermissions];
        return perm?.view || perm?.create || perm?.edit || perm?.delete;
      });

    if (!hasAnyPermissions) {
      newErrors.permissions = 'يرجى اختيار قسم واحد على الأقل';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      // عرض أول خطأ في النموذج
      const firstError = Object.values(errors)[0];
      if (firstError) {
        toast({
          title: 'خطأ في البيانات',
          description: firstError,
          variant: 'destructive',
        });
      }
      return;
    }

    setIsLoading(true);

    try {
      const userData: User = {
        id: currentUser?.id || 0,
        name: name.trim(),
        username: username.trim(),
        email: email.trim() || undefined,
        phone: phone.trim() || undefined,
        photo,
        role,
        permissions: permissions,
        status: currentUser?.status || 'Active',
        lastLogin: currentUser?.lastLogin,
      };

      await onSave(userData, password || undefined);

      toast({
        title: 'تم الحفظ بنجاح',
        description: `تم ${currentUser ? 'تحديث' : 'إضافة'} المستخدم بنجاح.`,
      });

      onClose();
    } catch (error) {
      console.error('Error saving user:', error);
      toast({
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ أثناء حفظ بيانات المستخدم. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (open === false) onClose();
      }}
    >
      <DialogContent className="sm:max-w-3xl max-h-[85vh] flex flex-col p-0">
        <DialogHeader className="p-4 pb-3 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
          <DialogTitle className="text-lg font-semibold text-gray-800">
            {currentUser ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}
          </DialogTitle>
        </DialogHeader>
        <form
          key={`user-form-${currentUser?.id || 'new'}`}
          onSubmit={handleSubmit}
          className="flex-1 flex flex-col overflow-hidden"
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="mx-4 mt-2 grid w-auto grid-cols-2 bg-gray-100">
              <TabsTrigger value="basic" className="text-sm">المعلومات الأساسية</TabsTrigger>
              <TabsTrigger value="permissions" className="text-sm">الصلاحيات</TabsTrigger>
            </TabsList>
            <TabsContent
              value="basic"
              className="flex-1 overflow-y-auto p-4"
            >
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                {/* Photo Section - Smaller and more compact */}
                <div className="lg:col-span-1 flex flex-col items-center gap-2">
                  <Avatar className="h-20 w-20 border-2 border-gray-200">
                    <AvatarImage src={photoPreview} alt={name} />
                    <AvatarFallback className="text-lg font-semibold bg-gradient-to-br from-blue-100 to-indigo-100">
                      {name.charAt(0) || '👤'}
                    </AvatarFallback>
                  </Avatar>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="text-xs px-3 py-1"
                    onClick={() => photoInputRef.current?.click()}
                  >
                    تغيير الصورة
                  </Button>
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoChange}
                    ref={photoInputRef}
                    className="hidden"
                  />
                </div>

                {/* Form Fields - Better organized */}
                <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-1.5 md:col-span-2">
                    <Label htmlFor="name" className="text-sm font-medium text-white">
                      الاسم الكامل *
                    </Label>
                    <Input
                      id="name"
                      value={name}
                      onChange={(e) => {
                        setName(e.target.value);
                        if (errors.name) {
                          setErrors(prev => ({ ...prev, name: '' }));
                        }
                      }}
                      required
                      className={`h-9 ${errors.name ? 'border-red-500 focus:border-red-500' : ''}`}
                      placeholder="أدخل الاسم الكامل"
                    />
                    {errors.name && <p className="text-xs text-red-600 mt-1">{errors.name}</p>}
                  </div>

                  <div className="space-y-1.5">
                    <Label htmlFor="username" className="text-sm font-medium text-white">
                      اسم المستخدم *
                    </Label>
                    <Input
                      id="username"
                      value={username}
                      onChange={(e) => {
                        setUsername(e.target.value);
                        if (errors.username) {
                          setErrors(prev => ({ ...prev, username: '' }));
                        }
                      }}
                      required
                      className={`h-9 ${errors.username ? 'border-red-500 focus:border-red-500' : ''}`}
                      placeholder="اسم المستخدم للدخول"
                    />
                    {errors.username && <p className="text-xs text-red-600 mt-1">{errors.username}</p>}
                  </div>

                  <div className="space-y-1.5">
                    <Label htmlFor="role" className="text-sm font-medium text-white">
                      المسمى الوظيفي
                    </Label>
                    <Select value={role} onValueChange={setRole}>
                      <SelectTrigger id="role" className="h-9">
                        <SelectValue placeholder="اختر المسمى الوظيفي" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="إدارة">إدارة</SelectItem>
                        <SelectItem value="مساعد للإدارة">مساعد للإدارة</SelectItem>
                        <SelectItem value="مخازن">مخازن</SelectItem>
                        <SelectItem value="صيانة">صيانة</SelectItem>
                        <SelectItem value="فحص وتقييم">فحص وتقييم</SelectItem>
                        <SelectItem value="مبيعات">مبيعات</SelectItem>
                        <SelectItem value="خدمة عملاء">خدمة عملاء</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-1.5">
                    <Label htmlFor="email" className="text-sm font-medium text-white">
                      البريد الإلكتروني (اختياري)
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => {
                        setEmail(e.target.value);
                        if (errors.email) {
                          setErrors(prev => ({ ...prev, email: '' }));
                        }
                      }}
                      className={`h-9 ${errors.email ? 'border-red-500 focus:border-red-500' : ''}`}
                      placeholder="<EMAIL>"
                    />
                    {errors.email && <p className="text-xs text-red-600 mt-1">{errors.email}</p>}
                  </div>

                  <div className="space-y-1.5">
                    <Label htmlFor="phone" className="text-sm font-medium text-white">
                      رقم الهاتف (اختياري)
                    </Label>
                    <Input
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="h-9"
                      placeholder="05xxxxxxxx"
                    />
                  </div>

                  <div className="space-y-1.5">
                    <Label htmlFor="password" className="text-sm font-medium text-white">
                      كلمة المرور {!currentUser && '*'}
                    </Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => {
                        setPassword(e.target.value);
                        if (errors.password) {
                          setErrors(prev => ({ ...prev, password: '' }));
                        }
                      }}
                      className={`h-9 ${errors.password ? 'border-red-500 focus:border-red-500' : ''}`}
                      placeholder={
                        currentUser ? 'اتركها فارغة لعدم التغيير' : 'أدخل كلمة مرور قوية'
                      }
                    />
                    {errors.password && <p className="text-xs text-red-600 mt-1">{errors.password}</p>}
                  </div>

                  <div className="space-y-1.5">
                    <Label htmlFor="confirmPassword" className="text-sm font-medium text-white">
                      تأكيد كلمة المرور {!currentUser && '*'}
                    </Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => {
                        setConfirmPassword(e.target.value);
                        if (errors.confirmPassword) {
                          setErrors(prev => ({ ...prev, confirmPassword: '' }));
                        }
                      }}
                      className={`h-9 ${errors.confirmPassword ? 'border-red-500 focus:border-red-500' : ''}`}
                      placeholder="أعد إدخال كلمة المرور"
                    />
                    {errors.confirmPassword && <p className="text-xs text-red-600 mt-1">{errors.confirmPassword}</p>}
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent
              key={`permissions-${currentUser?.id || 'new'}`}
              value="permissions"
              className="flex-1 overflow-y-auto p-4"
            >
              {errors.permissions && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">{errors.permissions}</p>
                </div>
              )}
              <PermissionsSection
                currentUserPermissions={permissions}
                onPermissionsChange={(newPermissions) => {
                  setPermissions(newPermissions);
                  // إزالة خطأ الصلاحيات عند التحديث
                  if (errors.permissions) {
                    setErrors(prev => ({ ...prev, permissions: '' }));
                  }
                }}
                currentUserId={currentUser?.id}
              />
            </TabsContent>
          </Tabs>
          <DialogFooter className="p-4 pt-3 border-t bg-gray-50 flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
              className="flex-1 h-10"
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>جاري الحفظ...</span>
                </div>
              ) : (
                currentUser ? '💾 حفظ التغييرات' : '👤 إنشاء المستخدم'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
