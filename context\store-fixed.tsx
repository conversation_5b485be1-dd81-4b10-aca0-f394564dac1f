"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { apiClient } from "@/lib/api-client";

// Define interfaces for type safety
interface User {
  id: number;
  name: string;
  username: string;
  email: string;
  permissions: Record<string, boolean>;
}

interface Device {
  id: string;
  modelId: number;
  status: string;
  warehouseId: number;
  serialNumber?: string;
  condition?: string;
  purchasePrice?: number;
  salePrice?: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Contact {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  type: "client" | "supplier";
  createdAt: Date;
  updatedAt: Date;
}

interface Warehouse {
  id: number;
  name: string;
  location?: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Sale {
  id: number;
  soNumber: string;
  clientName: string;
  clientPhone?: string;
  items: Array<{
    deviceId: string;
    salePrice: number;
  }>;
  totalAmount: number;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Return {
  id: number;
  returnNumber: string;
  saleId: number;
  deviceId: string;
  reason: string;
  refundAmount: number;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Activity {
  id: string;
  type: string;
  description: string;
  timestamp: Date;
}

interface Manufacturer {
  id: number;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

interface DeviceModel {
  id: number;
  name: string;
  manufacturerId: number;
  specifications?: any;
  createdAt: Date;
  updatedAt: Date;
}

interface SupplyOrder {
  id: number;
  orderNumber: string;
  supplierId: number;
  items: Array<{
    deviceModelId: number;
    quantity: number;
    unitPrice: number;
  }>;
  status: string;
  totalAmount: number;
  orderDate: Date;
  expectedDeliveryDate?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface EvaluationOrder {
  id: number;
  orderNumber: string;
  clientName: string;
  clientPhone?: string;
  items: Array<{
    deviceId: string;
    evaluationPrice: number;
    condition: string;
    notes?: string;
  }>;
  totalAmount: number;
  status: string;
  evaluationDate: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface MaintenanceLog {
  id: number;
  deviceId: string;
  type: string;
  description: string;
  cost?: number;
  status: string;
  startDate: Date;
  completedDate?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface WarehouseTransfer {
  id: number;
  deviceId: string;
  fromWarehouseId: number;
  toWarehouseId: number;
  reason?: string;
  status: string;
  transferDate: Date;
  completedDate?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface SystemSettings {
  id: number;
  logoUrl: string;
  companyNameAr: string;
  companyNameEn: string;
  addressAr: string;
  addressEn: string;
  phone: string;
  email: string;
  website: string;
  footerTextAr: string;
  footerTextEn: string;
  createdAt: Date;
  updatedAt: Date;
}

interface EmployeeRequest {
  id: number;
  type: string;
  description: string;
  status: string;
  requesterId: number;
  createdAt: Date;
  updatedAt: Date;
}

interface InternalMessage {
  id: number;
  fromUserId: number;
  toUserId: number;
  subject: string;
  content: string;
  isRead: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AcceptanceOrder {
  id: number;
  orderNumber: string;
  items: Array<{
    deviceId: string;
    acceptancePrice: number;
    condition: string;
  }>;
  totalAmount: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

interface MaintenanceOrder {
  id: number;
  orderNumber: string;
  clientName: string;
  items: Array<{
    deviceId: string;
    issueDescription: string;
    repairCost: number;
  }>;
  totalAmount: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

interface DeliveryOrder {
  id: number;
  orderNumber: string;
  clientName: string;
  items: Array<{
    deviceId: string;
    deliveryFee: number;
  }>;
  totalAmount: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

interface MaintenanceReceiptOrder {
  id: number;
  orderNumber: string;
  clientName: string;
  items: Array<{
    deviceId: string;
    receiptNotes: string;
  }>;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

interface DeviceReturnHistory {
  id: number;
  deviceId: string;
  reason: string;
  returnDate: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Stocktake {
  id: number;
  name: string;
  status: string;
  startDate: Date;
  endDate?: Date;
  items: Array<{
    deviceId: string;
    expectedQuantity: number;
    actualQuantity: number;
    discrepancy: number;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

// Store context interface
interface StoreContextType {
  isLoading: boolean;
  devices: Device[];
  clients: Contact[];
  suppliers: Contact[];
  warehouses: Warehouse[];
  sales: Sale[];
  returns: Return[];
  activities: Activity[];
  manufacturers: Manufacturer[];
  deviceModels: DeviceModel[];
  supplyOrders: SupplyOrder[];
  evaluationOrders: EvaluationOrder[];
  maintenanceHistory: MaintenanceLog[];
  warehouseTransfers: WarehouseTransfer[];
  users: User[];
  currentUser: User | null;
  systemSettings: SystemSettings;
  employeeRequests: EmployeeRequest[];
  internalMessages: InternalMessage[];
  acceptanceOrders: AcceptanceOrder[];
  maintenanceOrders: MaintenanceOrder[];
  deliveryOrders: DeliveryOrder[];
  maintenanceReceiptOrders: MaintenanceReceiptOrder[];
  deviceReturnHistory: DeviceReturnHistory[];
  stocktakes: Stocktake[];

  // Device functions
  addDevice: (device: Omit<Device, "id" | "createdAt" | "updatedAt">) => Promise<void>;
  updateDevice: (device: Device) => void;
  deleteDevice: (id: string) => void;
  updateDeviceStatus: (deviceId: string, status: string) => void;
  getDevicesByStatus: (status: string) => Device[];
  getDevicesByIds: (ids: string[]) => Device[];

  // Contact functions
  addContact: (contact: Omit<Contact, "id" | "createdAt" | "updatedAt">) => Promise<void>;
  updateContact: (contact: Contact) => Promise<void>;
  deleteContact: (id: number, type: "client" | "supplier") => Promise<void>;
  checkClientRelations: (clientId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  checkSupplierRelations: (supplierId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Warehouse functions
  addWarehouse: (warehouse: Omit<Warehouse, "id" | "createdAt" | "updatedAt">) => Promise<void>;
  updateWarehouse: (warehouse: Warehouse) => Promise<void>;
  deleteWarehouse: (id: number) => Promise<void>;
  checkWarehouseRelations: (warehouseId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Sales functions
  addSale: (sale: Omit<Sale, "id" | "soNumber" | "createdAt" | "updatedAt">) => Promise<void>;
  updateSale: (sale: Sale) => void;
  deleteSale: (id: number) => void;
  checkSaleRelations: (saleId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Return functions
  addReturn: (returnData: Omit<Return, "id" | "returnNumber" | "createdAt" | "updatedAt">) => Promise<void>;
  updateReturn: (returnData: Return) => Promise<void>;
  deleteReturn: (id: number) => Promise<void>;
  checkReturnRelations: (returnId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  isDeviceReturned: (deviceId: string) => boolean;
  canDeviceBeReturned: (deviceId: string) => { canReturn: boolean; reason?: string };
  addDeviceReturnHistory: (returnHistory: Omit<DeviceReturnHistory, "id" | "createdAt" | "updatedAt">) => void;

  // Manufacturer and model functions
  addManufacturer: (name: string) => Manufacturer;
  addDeviceModel: (model: Omit<DeviceModel, "id" | "createdAt" | "updatedAt">) => Promise<void>;

  // Supply order functions
  addSupplyOrder: (order: Omit<SupplyOrder, "id" | "orderNumber" | "createdAt" | "updatedAt">) => Promise<void>;
  updateSupplyOrder: (order: SupplyOrder) => Promise<void>;
  deleteSupplyOrder: (id: number) => void;
  checkSupplyOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Evaluation order functions
  addEvaluationOrder: (order: Omit<EvaluationOrder, "id" | "orderNumber" | "createdAt" | "updatedAt">) => Promise<void>;
  updateEvaluationOrder: (order: EvaluationOrder) => Promise<void>;
  deleteEvaluationOrder: (id: number) => Promise<void>;
  checkEvaluationOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Maintenance functions
  addMaintenanceLog: (log: Omit<MaintenanceLog, "id" | "createdAt" | "updatedAt">) => void;
  acknowledgeMaintenanceLog: (id: number) => void;
  revertDeviceToMaintenance: (deviceId: string) => void;

  // Warehouse transfer functions
  addWarehouseTransfer: (transfer: Omit<WarehouseTransfer, "id" | "createdAt" | "updatedAt">) => void;
  updateWarehouseTransfer: (transfer: WarehouseTransfer) => void;
  deleteWarehouseTransfer: (id: number) => void;
  completeWarehouseTransfer: (id: number) => void;

  // User functions
  addUser: (user: Omit<User, "id">) => Promise<void>;
  updateUser: (user: User) => Promise<void>;
  deleteUser: (id: number) => Promise<void>;
  setCurrentUser: (user: User | null) => void;

  // System settings
  updateSystemSettings: (settings: Partial<SystemSettings>) => void;

  // Employee requests
  addEmployeeRequest: (request: Omit<EmployeeRequest, "id" | "createdAt" | "updatedAt">) => void;
  processEmployeeRequest: (id: number, status: string) => void;

  // Internal messages
  sendMessage: (message: Omit<InternalMessage, "id" | "isRead" | "createdAt" | "updatedAt">) => void;
  updateMessage: (message: InternalMessage) => void;

  // Acceptance orders
  addAcceptanceOrder: (order: Omit<AcceptanceOrder, "id" | "orderNumber" | "createdAt" | "updatedAt">) => void;
  updateAcceptanceOrder: (order: AcceptanceOrder) => void;
  deleteAcceptanceOrder: (id: number) => void;

  // Maintenance orders
  addMaintenanceOrder: (order: Omit<MaintenanceOrder, "id" | "orderNumber" | "createdAt" | "updatedAt">) => void;
  updateMaintenanceOrder: (order: MaintenanceOrder) => void;
  deleteMaintenanceOrder: (id: number) => void;
  checkMaintenanceOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  checkDeviceRelationsInMaintenance: (deviceId: string, currentMaintenanceOrderId?: number) => { hasRelations: boolean; relatedOperations: string[] };

  // Delivery orders
  addDeliveryOrder: (order: Omit<DeliveryOrder, "id" | "orderNumber" | "createdAt" | "updatedAt">) => void;
  updateDeliveryOrder: (order: DeliveryOrder) => void;
  deleteDeliveryOrder: (id: number) => void;
  checkDeliveryOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Maintenance receipt orders
  addMaintenanceReceiptOrder: (order: Omit<MaintenanceReceiptOrder, "id" | "orderNumber" | "createdAt" | "updatedAt">) => void;
  updateMaintenanceReceiptOrder: (order: MaintenanceReceiptOrder) => void;
  deleteMaintenanceReceiptOrder: (id: number) => void;

  // Stocktake functions
  addStocktake: (stocktake: Omit<Stocktake, "id" | "createdAt" | "updatedAt">) => void;
  updateStocktake: (stocktake: Stocktake) => void;
  deleteStocktake: (id: number) => void;
  getFilteredStocktakes: (status?: string) => Stocktake[];
  addStocktakeItem: (stocktakeId: number, item: any) => void;
  updateStocktakeItem: (stocktakeId: number, itemId: number, updates: any) => void;
  addStocktakeDiscrepancy: (stocktakeId: number, discrepancy: any) => void;
  resolveStocktakeDiscrepancy: (stocktakeId: number, discrepancyId: number) => void;
  changeStocktakeStatus: (stocktakeId: number, status: string) => void;
  reviewStocktake: (stocktakeId: number) => void;

  // Backup and restore
  createBackupSnapshot: () => any;
  restoreFromSnapshot: (snapshot: any) => void;
  exportStoreData: () => any;
  importStoreData: (data: any) => void;
  getAuthHeader: () => Record<string, string>;
  addActivity: (activity: Omit<Activity, "id" | "timestamp">) => void;
}

// Create the context
const StoreContext = createContext<StoreContextType | null>(null);

export function StoreProvider({ children }: { children: React.ReactNode }) {
  // State variables
  const [isLoading, setIsLoading] = useState(false);
  const [devices, setDevices] = useState<Device[]>([]);
  const [clients, setClients] = useState<Contact[]>([]);
  const [suppliers, setSuppliers] = useState<Contact[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [returns, setReturns] = useState<Return[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [manufacturers, setManufacturers] = useState<Manufacturer[]>([]);
  const [deviceModels, setDeviceModels] = useState<DeviceModel[]>([]);
  const [supplyOrders, setSupplyOrders] = useState<SupplyOrder[]>([]);
  const [evaluationOrders, setEvaluationOrders] = useState<EvaluationOrder[]>([]);
  const [maintenanceHistory, setMaintenanceHistory] = useState<MaintenanceLog[]>([]);
  const [warehouseTransfers, setWarehouseTransfers] = useState<WarehouseTransfer[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    id: 1,
    logoUrl: "",
    companyNameAr: "",
    companyNameEn: "",
    addressAr: "",
    addressEn: "",
    phone: "",
    email: "",
    website: "",
    footerTextAr: "",
    footerTextEn: "",
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  const [employeeRequests, setEmployeeRequests] = useState<EmployeeRequest[]>([]);
  const [internalMessages, setInternalMessages] = useState<InternalMessage[]>([]);
  const [acceptanceOrders, setAcceptanceOrders] = useState<AcceptanceOrder[]>([]);
  const [maintenanceOrders, setMaintenanceOrders] = useState<MaintenanceOrder[]>([]);
  const [deliveryOrders, setDeliveryOrders] = useState<DeliveryOrder[]>([]);
  const [maintenanceReceiptOrders, setMaintenanceReceiptOrders] = useState<MaintenanceReceiptOrder[]>([]);
  const [deviceReturnHistory, setDeviceReturnHistory] = useState<DeviceReturnHistory[]>([]);
  const [stocktakes, setStocktakes] = useState<Stocktake[]>([]);

  // Helper functions
  const addActivity = (activity: Omit<Activity, "id" | "timestamp">) => {
    const newActivity = {
      ...activity,
      id: Date.now().toString(),
      timestamp: new Date(),
    };
    setActivities((prev) => [newActivity, ...prev].slice(0, 100));
  };

  const getAuthHeader = () => {
    if (!currentUser) {
      return { 'Authorization': `Bearer ${btoa('user:admin:admin')}` };
    }
    const token = btoa(`user:${currentUser.username || currentUser.name}:admin`);
    return { 'Authorization': `Bearer ${token}` };
  };

  // Evaluation order functions
  const addEvaluationOrder = async (order: Omit<EvaluationOrder, "id" | "orderNumber" | "createdAt" | "updatedAt">) => {
    try {
      const response = await apiClient.post("/api/evaluations", order);
      
      if (!response.ok) {
        throw new Error("Failed to create evaluation order");
      }

      const newOrder = await response.json();
      setEvaluationOrders((prev) => [newOrder, ...prev].sort((a, b) => b.id - a.id));

      addActivity({
        type: "evaluation",
        description: `تم إنشاء أمر تقييم رقم ${newOrder.orderNumber} للعميل ${newOrder.clientName}`,
      });
    } catch (error) {
      console.error("Failed to add evaluation order:", error);
      throw error;
    }
  };

  const updateEvaluationOrder = async (order: EvaluationOrder) => {
    try {
      const response = await apiClient.put("/api/evaluations", order);
      
      if (!response.ok) {
        throw new Error("Failed to update evaluation order");
      }

      const updatedOrder = await response.json();
      setEvaluationOrders((prev) =>
        prev.map((o) => (o.id === order.id ? updatedOrder : o))
      );

      addActivity({
        type: "evaluation",
        description: `تم تحديث أمر التقييم رقم ${order.orderNumber}`,
      });
    } catch (error) {
      console.error("Failed to update evaluation order:", error);
      throw error;
    }
  };

  const deleteEvaluationOrder = async (id: number) => {
    try {
      const response = await apiClient.delete(`/api/evaluations?id=${id}`);
      
      if (!response.ok) {
        throw new Error("Failed to delete evaluation order");
      }

      const orderToDelete = evaluationOrders.find((o) => o.id === id);
      setEvaluationOrders((prev) => prev.filter((o) => o.id !== id));

      if (orderToDelete) {
        addActivity({
          type: "evaluation",
          description: `تم حذف أمر التقييم رقم ${orderToDelete.orderNumber}`,
        });
      }
    } catch (error) {
      console.error("Failed to delete evaluation order:", error);
      throw error;
    }
  };

  const checkEvaluationOrderRelations = (orderId: number) => {
    const order = evaluationOrders.find(o => o.id === orderId);
    if (!order) {
      return { canDelete: false, reason: "أمر التقييم غير موجود" };
    }

    const relatedOperations: string[] = [];

    // Check if any device in this order appears in later operations in any section
    for (const item of order.items) {
      // Check sales
      const deviceInSales = sales.some(sale => 
        sale.items.some(saleItem => saleItem.deviceId === item.deviceId) &&
        new Date(sale.createdAt) > new Date(order.createdAt)
      );
      if (deviceInSales) {
        relatedOperations.push(`الجهاز ${item.deviceId} مُستخدم في عمليات بيع لاحقة`);
      }

      // Check maintenance
      const deviceInMaintenance = maintenanceHistory.some(maintenance => 
        maintenance.deviceId === item.deviceId &&
        new Date(maintenance.createdAt) > new Date(order.createdAt)
      );
      if (deviceInMaintenance) {
        relatedOperations.push(`الجهاز ${item.deviceId} مُستخدم في عمليات صيانة لاحقة`);
      }

      // Check warehouse transfers
      const deviceInTransfers = warehouseTransfers.some(transfer => 
        transfer.deviceId === item.deviceId &&
        new Date(transfer.createdAt) > new Date(order.createdAt)
      );
      if (deviceInTransfers) {
        relatedOperations.push(`الجهاز ${item.deviceId} مُستخدم في عمليات نقل مخزني لاحقة`);
      }

      // Check other evaluation orders (later ones)
      const deviceInLaterEvaluations = evaluationOrders.some(evalOrder => 
        evalOrder.id !== orderId &&
        evalOrder.items.some(evalItem => evalItem.deviceId === item.deviceId) &&
        new Date(evalOrder.createdAt) > new Date(order.createdAt)
      );
      if (deviceInLaterEvaluations) {
        relatedOperations.push(`الجهاز ${item.deviceId} مُستخدم في أوامر تقييم لاحقة`);
      }

      // Check maintenance orders
      const deviceInMaintenanceOrders = maintenanceOrders.some(maintOrder => 
        maintOrder.items.some(maintItem => maintItem.deviceId === item.deviceId) &&
        new Date(maintOrder.createdAt) > new Date(order.createdAt)
      );
      if (deviceInMaintenanceOrders) {
        relatedOperations.push(`الجهاز ${item.deviceId} مُستخدم في أوامر صيانة لاحقة`);
      }

      // Check delivery orders
      const deviceInDeliveryOrders = deliveryOrders.some(delivOrder => 
        delivOrder.items.some(delivItem => delivItem.deviceId === item.deviceId) &&
        new Date(delivOrder.createdAt) > new Date(order.createdAt)
      );
      if (deviceInDeliveryOrders) {
        relatedOperations.push(`الجهاز ${item.deviceId} مُستخدم في أوامر توصيل لاحقة`);
      }
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: "لا يمكن حذف أمر التقييم لوجود عمليات لاحقة مرتبطة بالأجهزة",
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  // Create the context value
  const value: StoreContextType = {
    isLoading,
    devices,
    clients,
    suppliers,
    warehouses,
    sales,
    returns,
    activities,
    manufacturers,
    deviceModels,
    supplyOrders,
    evaluationOrders,
    maintenanceHistory,
    warehouseTransfers,
    users,
    currentUser,
    systemSettings,
    employeeRequests,
    internalMessages,
    acceptanceOrders,
    maintenanceOrders,
    deliveryOrders,
    maintenanceReceiptOrders,
    deviceReturnHistory,
    stocktakes,

    // Device functions
    addDevice: async () => {},
    updateDevice: () => {},
    deleteDevice: () => {},
    updateDeviceStatus: () => {},
    getDevicesByStatus: () => [],
    getDevicesByIds: () => [],

    // Contact functions
    addContact: async () => {},
    updateContact: async () => {},
    deleteContact: async () => {},
    checkClientRelations: () => ({ canDelete: true }),
    checkSupplierRelations: () => ({ canDelete: true }),

    // Warehouse functions
    addWarehouse: async () => {},
    updateWarehouse: async () => {},
    deleteWarehouse: async () => {},
    checkWarehouseRelations: () => ({ canDelete: true }),

    // Sales functions
    addSale: async () => {},
    updateSale: () => {},
    deleteSale: () => {},
    checkSaleRelations: () => ({ canDelete: true }),

    // Return functions
    addReturn: async () => {},
    updateReturn: async () => {},
    deleteReturn: async () => {},
    checkReturnRelations: () => ({ canDelete: true }),
    isDeviceReturned: () => false,
    canDeviceBeReturned: () => ({ canReturn: false }),
    addDeviceReturnHistory: () => {},

    // Manufacturer and model functions
    addManufacturer: () => ({ id: 0, name: "", createdAt: new Date(), updatedAt: new Date() }),
    addDeviceModel: async () => {},

    // Supply order functions
    addSupplyOrder: async () => {},
    updateSupplyOrder: async () => {},
    deleteSupplyOrder: () => {},
    checkSupplyOrderRelations: () => ({ canDelete: true }),

    // Evaluation order functions
    addEvaluationOrder,
    updateEvaluationOrder,
    deleteEvaluationOrder,
    checkEvaluationOrderRelations,

    // Maintenance functions
    addMaintenanceLog: () => {},
    acknowledgeMaintenanceLog: () => {},
    revertDeviceToMaintenance: () => {},

    // Warehouse transfer functions
    addWarehouseTransfer: () => {},
    updateWarehouseTransfer: () => {},
    deleteWarehouseTransfer: () => {},
    completeWarehouseTransfer: () => {},

    // User functions
    addUser: async () => {},
    updateUser: async () => {},
    deleteUser: async () => {},
    setCurrentUser: setCurrentUser,

    // System settings
    updateSystemSettings: () => {},

    // Employee requests
    addEmployeeRequest: () => {},
    processEmployeeRequest: () => {},

    // Internal messages
    sendMessage: () => {},
    updateMessage: () => {},

    // Acceptance orders
    addAcceptanceOrder: () => {},
    updateAcceptanceOrder: () => {},
    deleteAcceptanceOrder: () => {},

    // Maintenance orders
    addMaintenanceOrder: () => {},
    updateMaintenanceOrder: () => {},
    deleteMaintenanceOrder: () => {},
    checkMaintenanceOrderRelations: () => ({ canDelete: true }),
    checkDeviceRelationsInMaintenance: () => ({ hasRelations: false, relatedOperations: [] }),

    // Delivery orders
    addDeliveryOrder: () => {},
    updateDeliveryOrder: () => {},
    deleteDeliveryOrder: () => {},
    checkDeliveryOrderRelations: () => ({ canDelete: true }),

    // Maintenance receipt orders
    addMaintenanceReceiptOrder: () => {},
    updateMaintenanceReceiptOrder: () => {},
    deleteMaintenanceReceiptOrder: () => {},

    // Stocktake functions
    addStocktake: () => {},
    updateStocktake: () => {},
    deleteStocktake: () => {},
    getFilteredStocktakes: () => [],
    addStocktakeItem: () => {},
    updateStocktakeItem: () => {},
    addStocktakeDiscrepancy: () => {},
    resolveStocktakeDiscrepancy: () => {},
    changeStocktakeStatus: () => {},
    reviewStocktake: () => {},

    // Backup and restore
    createBackupSnapshot: () => ({}),
    restoreFromSnapshot: () => {},
    exportStoreData: () => ({}),
    importStoreData: () => {},
    getAuthHeader,
    addActivity,
  };

  return (
    <StoreContext.Provider value={value}>
      {children}
    </StoreContext.Provider>
  );
}

export function useStore() {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error("useStore must be used within a StoreProvider");
  }
  return context;
}
