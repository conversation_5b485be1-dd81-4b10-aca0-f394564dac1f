// إنشاء token للمستخدم admin
function createAdminToken() {
  const username = 'admin';
  const role = 'admin';
  
  // إنشاء الـ token بصيغة: user:username:role
  const tokenData = `user:${username}:${role}`;
  
  // ترميز base64
  const token = btoa(tokenData);
  
  console.log('🔑 Token للمستخدم admin:');
  console.log('───────────────────────────────');
  console.log(`Username: ${username}`);
  console.log(`Role: ${role}`);
  console.log(`Token Data: ${tokenData}`);
  console.log(`Base64 Token: ${token}`);
  console.log('───────────────────────────────');
  console.log('');
  console.log('📋 لاستخدام هذا الـ Token:');
  console.log('في Headers ضع:');
  console.log(`Authorization: Bearer ${token}`);
  console.log('');
  console.log('أو في متصفح DevTools:');
  console.log(`localStorage.setItem('authToken', '${token}');`);
  
  return token;
}

// تشغيل الدالة
createAdminToken();
