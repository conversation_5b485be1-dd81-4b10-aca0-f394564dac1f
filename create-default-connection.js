// إنشاء اتصال افتراضي تلقائياً
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createDefaultConnection() {
  try {
    console.log('🔄 التحقق من وجود اتصالات قواعد البيانات...');
    
    // التحقق من وجود اتصالات
    const existingConnections = await prisma.databaseConnection.findMany();
    
    if (existingConnections.length > 0) {
      console.log(`✅ يوجد ${existingConnections.length} اتصال موجود:`);
      
      let hasActiveConnection = false;
      existingConnections.forEach((conn, index) => {
        console.log(`${index + 1}. ${conn.name} - نشط: ${conn.isActive ? 'نعم' : 'لا'}`);
        if (conn.isActive) hasActiveConnection = true;
      });
      
      // إذا لم يوجد اتصال نشط، فعّل الأول
      if (!hasActiveConnection) {
        console.log('⚠️ لا يوجد اتصال نشط، جاري تفعيل الاتصال الأول...');
        await prisma.databaseConnection.update({
          where: { id: existingConnections[0].id },
          data: { isActive: true }
        });
        console.log(`✅ تم تفعيل الاتصال: ${existingConnections[0].name}`);
      }
      
      return;
    }

    console.log('📝 إنشاء اتصال افتراضي...');
    
    // إنشاء اتصال افتراضي
    const defaultConnection = await prisma.databaseConnection.create({
      data: {
        name: 'الاتصال الافتراضي',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT) || 5432,
        database: process.env.DB_NAME || 'deviceflow_db',
        username: process.env.DB_USER || 'deviceflow_user',
        password: process.env.DB_PASSWORD || 'om772828', // كلمة المرور الأصلية لـ pg_dump
        isActive: true,
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('✅ تم إنشاء الاتصال الافتراضي بنجاح:', {
      id: defaultConnection.id,
      name: defaultConnection.name,
      host: defaultConnection.host,
      database: defaultConnection.database
    });

    // التحقق من وجود مستخدم admin
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.log('📝 إنشاء مستخدم admin افتراضي...');
      const hashedPassword = await bcrypt.hash('admin', 10);
      
      const newAdmin = await prisma.user.create({
        data: {
          username: 'admin',
          password: hashedPassword,
          role: 'admin',
          email: '<EMAIL>',
          isActive: true
        }
      });

      console.log('✅ تم إنشاء مستخدم admin:', {
        id: newAdmin.id,
        username: newAdmin.username,
        role: newAdmin.role
      });
    }

  } catch (error) {
    console.error('❌ خطأ في إنشاء الاتصال الافتراضي:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createDefaultConnection();
