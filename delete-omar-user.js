const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function deleteOmarUser() {
  try {
    console.log('🗑️ حذف المستخدم omar albkri...\n');
    
    // البحث عن المستخدم
    const omarUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'albakrepc2' },
          { name: { contains: 'omar', mode: 'insensitive' } }
        ]
      }
    });
    
    if (!omarUser) {
      console.log('❌ لم يتم العثور على المستخدم omar albkri');
      return;
    }
    
    console.log('✅ تم العثور على المستخدم:');
    console.log(`   المعرف: ${omarUser.id}`);
    console.log(`   اسم المستخدم: ${omarUser.username}`);
    console.log(`   الاسم: ${omarUser.name}`);
    console.log(`   الدور: ${omarUser.role}`);
    
    // التأكد من أنه ليس المستخدم الوحيد
    const totalUsers = await prisma.user.count();
    if (totalUsers <= 1) {
      console.log('❌ لا يمكن حذف المستخدم الوحيد في النظام');
      return;
    }
    
    // حذف المستخدم
    await prisma.user.delete({
      where: { id: omarUser.id }
    });
    
    console.log('✅ تم حذف المستخدم omar albkri نهائياً');
    
    // عرض المستخدمين المتبقين
    console.log('\n📊 المستخدمين المتبقين:');
    const remainingUsers = await prisma.user.findMany({
      orderBy: [
        { role: 'desc' },
        { createdAt: 'asc' }
      ],
      select: {
        id: true,
        username: true,
        name: true,
        role: true,
        status: true
      }
    });
    
    remainingUsers.forEach((user, index) => {
      const marker = index === 0 ? '👑 [افتراضي]' : '';
      const statusIcon = user.status === 'Active' ? '✅' : '❌';
      console.log(`${index + 1}. ${user.username} - ${user.name} (${user.role}) ${statusIcon} ${marker}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في حذف المستخدم:', error);
  } finally {
    await prisma.$disconnect();
  }
}

deleteOmarUser();
