/**
 * API Client Utility للتعامل مع طلبات HTTP مع headers التفويض
 */

interface ApiRequestOptions extends RequestInit {
  headers?: Record<string, string>;
}

/**
 * إنشاء headers افتراضية مع التفويض
 */
function createAuthHeaders(): Record<string, string> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // للبيئة التطويرية، سنستخدم token مبسط
  // في الإنتاج، يجب الحصول على token من login
  const devToken = btoa('user:admin:admin'); // user:username:role
  headers['Authorization'] = `Bearer ${devToken}`;

  return headers;
}

// تأكد من إرسال التوكن مع كل طلب
const getAuthHeader = () => {
  const token = localStorage.getItem('authToken') || btoa('user:admin:admin');
  return {
    'Authorization': `Bearer ${token}`
  };
};

/**
 * دالة مساعدة لإرسال طلبات API مع التفويض
 */
export async function apiRequest(url: string, options: ApiRequestOptions = {}): Promise<Response> {
  const authHeaders = createAuthHeaders();
  
  const mergedOptions: RequestInit = {
    ...options,
    headers: {
      ...authHeaders,
      ...options.headers,
    },
  };

  return fetch(url, mergedOptions);
}

/**
 * دوال مساعدة لطلبات HTTP شائعة
 */
export const apiClient = {
  get: async (url: string) => {
    try {
      const response = await fetch(url, {
        headers: {
          ...getAuthHeader()
        }
      });
      if (!response.ok) throw new Error(`API error: ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error(`GET ${url} failed:`, error);
      throw error;
    }
  },
  
  post: async (url: string, data: any) => {
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeader()
        },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error(`API error: ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error(`POST ${url} failed:`, error);
      throw error;
    }
  },
    
  put: (url: string, data?: any, options?: ApiRequestOptions) => 
    apiRequest(url, { 
      method: 'PUT', 
      body: data ? JSON.stringify(data) : undefined,
      ...options 
    }),
    
  delete: (url: string, data?: any, options?: ApiRequestOptions) => 
    apiRequest(url, { 
      method: 'DELETE', 
      body: data ? JSON.stringify(data) : undefined,
      ...options 
    }),
};

/**
 * معالجة استجابة API مع الأخطاء
 */
export async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * دالة شاملة للتعامل مع API
 */
export async function apiCall<T>(url: string, options?: ApiRequestOptions): Promise<T> {
  const response = await apiRequest(url, options);
  return handleApiResponse<T>(response);
}
