const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function manageOmarUser() {
  try {
    console.log('🔍 البحث عن المستخدم omar albkri...\n');
    
    // البحث عن المستخدم
    const omarUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: { contains: 'omar', mode: 'insensitive' } },
          { name: { contains: 'omar', mode: 'insensitive' } },
          { username: 'albakrepc2' }
        ]
      }
    });
    
    if (!omarUser) {
      console.log('❌ لم يتم العثور على المستخدم omar albkri');
      return;
    }
    
    console.log('✅ تم العثور على المستخدم:');
    console.log(`   المعرف: ${omarUser.id}`);
    console.log(`   اسم المستخدم: ${omarUser.username}`);
    console.log(`   الاسم: ${omarUser.name}`);
    console.log(`   البريد: ${omarUser.email}`);
    console.log(`   الدور الحالي: ${omarUser.role}`);
    console.log(`   الحالة: ${omarUser.status}`);
    
    console.log('\n🔧 اختر الإجراء:');
    console.log('1. ترقية إلى مدير (admin) - صلاحيات كاملة');
    console.log('2. تغيير إلى مستخدم عادي (user) - صلاحيات محدودة');
    console.log('3. حذف المستخدم نهائياً');
    console.log('4. تعطيل المستخدم (إبقاؤه لكن غير نشط)');
    
    // للتشغيل التلقائي، سنختار الخيار الأول (ترقية إلى admin)
    const choice = 1;
    
    switch (choice) {
      case 1:
        // ترقية إلى admin
        await prisma.user.update({
          where: { id: omarUser.id },
          data: {
            role: 'admin',
            status: 'Active'
          }
        });
        console.log('✅ تم ترقية المستخدم إلى مدير بصلاحيات كاملة');
        break;
        
      case 2:
        // تغيير إلى user
        await prisma.user.update({
          where: { id: omarUser.id },
          data: {
            role: 'user',
            status: 'Active'
          }
        });
        console.log('✅ تم تغيير المستخدم إلى مستخدم عادي');
        break;
        
      case 3:
        // حذف المستخدم
        await prisma.user.delete({
          where: { id: omarUser.id }
        });
        console.log('✅ تم حذف المستخدم نهائياً');
        break;
        
      case 4:
        // تعطيل المستخدم
        await prisma.user.update({
          where: { id: omarUser.id },
          data: {
            status: 'Inactive'
          }
        });
        console.log('✅ تم تعطيل المستخدم');
        break;
    }
    
    // عرض المستخدمين المحدثين
    console.log('\n📊 المستخدمين بعد التحديث:');
    const allUsers = await prisma.user.findMany({
      orderBy: [
        { role: 'desc' },
        { createdAt: 'asc' }
      ],
      select: {
        id: true,
        username: true,
        name: true,
        role: true,
        status: true
      }
    });
    
    allUsers.forEach((user, index) => {
      const marker = index === 0 ? '👑 [افتراضي]' : '';
      const statusIcon = user.status === 'Active' ? '✅' : '❌';
      console.log(`${index + 1}. ${user.username} - ${user.name} (${user.role}) ${statusIcon} ${marker}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في إدارة المستخدم:', error);
  } finally {
    await prisma.$disconnect();
  }
}

manageOmarUser();
