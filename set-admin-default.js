const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function setAdminAsDefault() {
  try {
    console.log('🔧 ضبط المستخدم admin كمستخدم افتراضي...\n');
    
    // البحث عن المستخدم admin
    const adminUser = await prisma.user.findFirst({
      where: { 
        OR: [
          { username: 'admin' },
          { role: 'admin' }
        ]
      }
    });
    
    if (!adminUser) {
      console.log('❌ لم يتم العثور على مستخدم admin');
      return;
    }
    
    console.log(`✅ تم العثور على مستخدم admin:`);
    console.log(`   المعرف: ${adminUser.id}`);
    console.log(`   اسم المستخدم: ${adminUser.username}`);
    console.log(`   الاسم: ${adminUser.name || 'غير محدد'}`);
    console.log(`   الدور: ${adminUser.role}`);
    
    // تحديث ترتيب المستخدمين لجعل admin الأول
    // سنحدث تاريخ الإنشاء لجعل admin هو الأقدم
    const oldestDate = new Date('2020-01-01');
    
    await prisma.user.update({
      where: { id: adminUser.id },
      data: {
        createdAt: oldestDate,
        name: 'مدير النظام', // تحديث الاسم
        status: 'Active'
      }
    });
    
    console.log('✅ تم تحديث المستخدم admin ليكون الافتراضي');
    
    // عرض ترتيب المستخدمين الجديد
    const allUsers = await prisma.user.findMany({
      orderBy: { createdAt: 'asc' },
      select: {
        id: true,
        username: true,
        name: true,
        role: true,
        createdAt: true
      }
    });
    
    console.log('\n📊 ترتيب المستخدمين الجديد:');
    allUsers.forEach((user, index) => {
      const marker = index === 0 ? '👑 [افتراضي]' : '';
      console.log(`${index + 1}. ${user.username} - ${user.name} (${user.role}) ${marker}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في ضبط المستخدم الافتراضي:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setAdminAsDefault();
