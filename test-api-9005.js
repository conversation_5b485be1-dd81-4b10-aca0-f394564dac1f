const https = require('https');
const http = require('http');

// إنشاء توكن للمدير بالتنسيق الصحيح
function createToken(username, role) {
  const tokenData = `user:${username}:${role}`;
  return Buffer.from(tokenData).toString('base64');
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlParts = new URL(url);
    const requestOptions = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname + urlParts.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ ok: res.statusCode < 300, status: res.statusCode, json: () => jsonData });
        } catch (e) {
          resolve({ ok: res.statusCode < 300, status: res.statusCode, text: () => data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testAPI() {
  try {
    console.log('🔍 اختبار API على المنفذ 9005...\n');
    
    // إنشاء توكن مدير بالتنسيق الصحيح
    const adminToken = createToken('admin', 'admin');
    
    // اختبار API المستخدمين
    const response = await makeRequest('http://localhost:9005/api/users', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText || 'خطأ'}`);
    }
    
    const users = response.json();
    console.log(`📊 تم استرجاع ${users.length} مستخدم من API:`);
    console.log('───────────────────────────────');
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} - ${user.username} (${user.role})${user.role === 'admin' ? ' 👑' : ''}`);
      console.log(`   المعرف: ${user.id}`);
      console.log(`   البريد: ${user.email}`);
      console.log(`   الحالة: ${user.status}`);
      
      // فحص الصلاحيات
      if (user.permissions) {
        try {
          const permissions = typeof user.permissions === 'string' 
            ? JSON.parse(user.permissions) 
            : user.permissions;
            
          console.log(`   صلاحيات التوريد: view=${permissions.supply?.view}, create=${permissions.supply?.create}`);
          console.log(`   صلاحيات المخزون: view=${permissions.inventory?.view}, create=${permissions.inventory?.create}`);
        } catch (e) {
          console.log(`   ❌ خطأ في تحليل الصلاحيات: ${e.message}`);
        }
      } else {
        console.log(`   ⚠️ لا توجد صلاحيات`);
      }
      console.log('');
    });
    
    // التحقق من المستخدم الأول
    if (users.length > 0) {
      const firstUser = users[0];
      console.log(`✅ المستخدم الأول الذي سيتم تعيينه كـ currentUser:`);
      console.log(`   الاسم: ${firstUser.name}`);
      console.log(`   اسم المستخدم: ${firstUser.username}`);
      console.log(`   الدور: ${firstUser.role}`);
      
      if (firstUser.permissions) {
        try {
          const permissions = typeof firstUser.permissions === 'string' 
            ? JSON.parse(firstUser.permissions) 
            : firstUser.permissions;
          console.log(`   صلاحيات التوريد: ${JSON.stringify(permissions.supply || {})}`);
        } catch (e) {
          console.log(`   ❌ خطأ في تحليل صلاحيات المستخدم الأول`);
        }
      }
    }
    
    console.log('\n🔧 اختبار API الإعدادات...');
    const settingsResponse = await makeRequest('http://localhost:9005/api/settings', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    if (settingsResponse.ok) {
      const settings = settingsResponse.json();
      console.log(`✅ تم استرجاع الإعدادات بنجاح`);
      console.log(`   اسم الشركة: ${settings.companyName || 'غير محدد'}`);
    } else {
      console.log(`❌ فشل في استرجاع الإعدادات: ${settingsResponse.status}`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error.message);
  }
}

testAPI();
