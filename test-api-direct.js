// اختبار مباشر لـ API المستخدمين
async function testUsersAPI() {
  try {
    const authToken = Buffer.from('user:admin:admin').toString('base64');
    
    const response = await fetch('http://localhost:3000/api/users', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      console.log('❌ فشل في API:', response.status, response.statusText);
      return;
    }

    const users = await response.json();
    console.log('✅ تم استرجاع المستخدمين بنجاح:');
    console.log('📊 عدد المستخدمين:', users.length);
    
    users.forEach((user, index) => {
      console.log(`\n${index + 1}. ${user.name} (${user.username})`);
      console.log(`   المعرف: ${user.id}`);
      console.log(`   البريد: ${user.email}`);
      console.log(`   الدور: ${user.role}`);
      console.log(`   نوع الصلاحيات: ${typeof user.permissions}`);
      console.log(`   صلاحيات فارغة: ${!user.permissions}`);
      
      if (user.permissions) {
        console.log(`   صلاحيات supply.view: ${user.permissions.supply?.view}`);
        console.log(`   صلاحيات supply.create: ${user.permissions.supply?.create}`);
      }
    });

    // فحص المستخدم الأول (الذي سيصبح currentUser)
    if (users.length > 0) {
      const firstUser = users[0];
      console.log('\n🎯 المستخدم الأول (سيصبح currentUser):');
      console.log(`   الاسم: ${firstUser.name}`);
      console.log(`   اسم المستخدم: ${firstUser.username}`);
      console.log(`   الدور: ${firstUser.role}`);
      console.log(`   لديه صلاحيات: ${!!firstUser.permissions}`);
    }

  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error);
  }
}

testUsersAPI();
