const fetch = require('node-fetch');

async function testConnectionsAPI() {
  try {
    console.log('🔍 اختبار API اتصالات قواعد البيانات...\n');
    
    // تشفير بيانات المصادقة
    const credentials = Buffer.from('admin:admin').toString('base64');
    
    const response = await fetch('http://localhost:3000/api/database/connections', {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Content-Type': 'application/json'
      }
    });
    
    const status = response.status;
    console.log(`📊 حالة الاستجابة: ${status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ خطأ: ${errorText}`);
      return;
    }
    
    const result = await response.json();
    console.log(`✅ الاستجابة:`, JSON.stringify(result, null, 2));
    
    if (result.connections && result.connections.length > 0) {
      console.log(`\n📈 تم العثور على ${result.connections.length} اتصال:`);
      result.connections.forEach((conn, index) => {
        console.log(`${index + 1}. ${conn.name} - نشط: ${conn.isActive ? 'نعم' : 'لا'}`);
      });
      
      const activeCount = result.connections.filter(c => c.isActive).length;
      console.log(`\n✅ الاتصالات النشطة: ${activeCount}`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error);
  }
}

testConnectionsAPI();
