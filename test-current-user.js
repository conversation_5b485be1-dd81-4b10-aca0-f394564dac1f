async function testCurrentUser() {
  try {
    console.log('🔍 فحص API المستخدمين...');
    
    // Test direct fetch with global fetch for Node.js
    const authHeader = { 'Authorization': `Bearer ${Buffer.from('user:admin:admin').toString('base64')}` };
    
    // Try to use native fetch or install node-fetch
    let fetchFunc;
    try {
      fetchFunc = globalThis.fetch;
    } catch {
      try {
        fetchFunc = require('node-fetch');
      } catch {
        console.log('❌ Fetch not available. Please run with Node 18+ or install node-fetch');
        return;
      }
    }
    
    const response = await fetchFunc('http://localhost:3000/api/users', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...authHeader
      }
    });
    
    if (response.ok) {
      const users = await response.json();
      console.log('📊 المستخدمون المُحملون من API:');
      users.forEach(user => {
        console.log(`- ${user.name} (${user.username}) - ID: ${user.id}`);
        if (user.role === 'admin') {
          console.log('  ✅ هذا مدير');
        }
      });
      
      const adminUser = users.find(u => u.role === 'admin');
      if (adminUser) {
        console.log(`\n🎯 المستخدم الذي يجب أن يكون currentUser: ${adminUser.name}`);
        console.log(`📧 البريد: ${adminUser.email}`);
        console.log(`🔑 اسم المستخدم: ${adminUser.username}`);
      }
    } else {
      console.error('❌ فشل في تحميل المستخدمين:', response.status);
    }
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  }
}

testCurrentUser();
