// اختبار API أوامر التقييم
async function testEvaluationsAPI() {
  try {
    console.log('🔍 اختبار API أوامر التقييم...\n');
    
    // إنشاء headers التفويض
    const devToken = btoa('user:admin:admin');
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${devToken}`
    };
    
    // 1. اختبار GET - استرجاع أوامر التقييم
    console.log('📥 1. اختبار استرجاع أوامر التقييم...');
    const getResponse = await fetch('http://localhost:9005/api/evaluations', {
      method: 'GET',
      headers: headers
    });
    
    if (getResponse.ok) {
      const evaluations = await getResponse.json();
      console.log(`✅ تم استرجاع ${evaluations.length} أمر تقييم`);
      
      if (evaluations.length > 0) {
        console.log('📋 أوامر التقييم الموجودة:');
        evaluations.forEach((eval, index) => {
          console.log(`${index + 1}. ${eval.orderId} - ${eval.employeeName} - ${eval.date}`);
          console.log(`   عدد الأجهزة: ${eval.items ? eval.items.length : 0}`);
        });
      } else {
        console.log('⚠️ لا توجد أوامر تقييم محفوظة');
      }
    } else {
      console.error('❌ خطأ في استرجاع أوامر التقييم:', getResponse.status);
      const errorData = await getResponse.json();
      console.error('تفاصيل الخطأ:', errorData);
    }
    
    // 2. اختبار POST - إنشاء أمر تقييم جديد
    console.log('\n📤 2. اختبار إنشاء أمر تقييم جديد...');
    const testEvaluation = {
      employeeName: 'مدير النظام',
      date: new Date().toISOString(),
      items: [
        {
          deviceId: 'test-device-1',
          imei: '123456789012345',
          model: 'iPhone 14',
          finalGrade: 'جاهز للبيع',
          notes: 'جهاز في حالة ممتازة'
        }
      ],
      notes: 'اختبار أمر تقييم',
      status: 'مكتمل'
    };
    
    const postResponse = await fetch('http://localhost:9005/api/evaluations', {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(testEvaluation)
    });
    
    if (postResponse.ok) {
      const newEvaluation = await postResponse.json();
      console.log('✅ تم إنشاء أمر تقييم جديد:', newEvaluation.orderId);
      console.log(`   معرف الأمر: ${newEvaluation.orderId}`);
      console.log(`   الموظف: ${newEvaluation.employeeName}`);
      console.log(`   عدد الأجهزة: ${newEvaluation.items.length}`);
    } else {
      console.error('❌ خطأ في إنشاء أمر التقييم:', postResponse.status);
      const errorData = await postResponse.json();
      console.error('تفاصيل الخطأ:', errorData);
    }
    
    // 3. اختبار GET مرة أخرى لنرى الأمر الجديد
    console.log('\n🔄 3. اختبار استرجاع أوامر التقييم بعد الإضافة...');
    const getResponse2 = await fetch('http://localhost:9005/api/evaluations', {
      method: 'GET',
      headers: headers
    });
    
    if (getResponse2.ok) {
      const evaluations2 = await getResponse2.json();
      console.log(`✅ إجمالي أوامر التقييم الآن: ${evaluations2.length}`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error);
  }
}

testEvaluationsAPI();
