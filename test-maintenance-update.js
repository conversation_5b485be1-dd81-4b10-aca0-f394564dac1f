// اختبار دالة تحديث أوامر الصيانة
// هذا الملف للاختبار فقط

const testMaintenanceUpdate = async () => {
  try {
    console.log('🧪 Testing maintenance order update...');
    
    // بيانات تجريبية لأمر صيانة
    const testOrder = {
      id: 1, // يجب أن يكون ID موجود
      orderNumber: 'MAINT-1',
      referenceNumber: 'REF-TEST-001',
      date: new Date().toISOString(),
      employeeName: 'موظف اختبار',
      maintenanceEmployeeName: 'فني الصيانة',
      items: [
        {
          id: 'DEV-001',
          model: 'iPhone 14',
          imei: '123456789012345',
          status: 'قيد الإصلاح'
        }
      ],
      notes: 'تم تحديث الأمر للاختبار',
      status: 'wip',
      source: 'مخزن رئيسي',
      attachmentName: '',
      createdAt: new Date().toISOString()
    };

    // إرسال طلب التحديث
    const response = await fetch('/api/maintenance-orders', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testOrder),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ Update failed:', errorData);
      return false;
    }

    const updatedOrder = await response.json();
    console.log('✅ Update successful:', updatedOrder);
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
};

// تشغيل الاختبار
if (typeof window !== 'undefined') {
  // في المتصفح
  window.testMaintenanceUpdate = testMaintenanceUpdate;
  console.log('🔧 Test function loaded. Run: testMaintenanceUpdate()');
} else {
  // في Node.js
  testMaintenanceUpdate();
}
