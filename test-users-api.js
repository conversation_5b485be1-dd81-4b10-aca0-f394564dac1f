// اختبار API المستخدمين
async function testUsersAPI() {
  try {
    console.log('🔍 اختبار API المستخدمين...\n');
    
    // إنشاء headers التفويض
    const devToken = btoa('user:admin:admin'); // user:username:role
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${devToken}`
    };
    
    const response = await fetch('http://localhost:9005/api/users', {
      method: 'GET',
      headers: headers
    });
    
    if (!response.ok) {
      console.error('❌ خطأ في API:', response.status, response.statusText);
      return;
    }
    
    const users = await response.json();
    
    console.log(`📊 تم استرجاع ${users.length} مستخدم من API:`);
    console.log('───────────────────────────────');
    
    users.forEach((user, index) => {
      const marker = index === 0 ? '👑 [سيصبح currentUser]' : '';
      console.log(`${index + 1}. ${user.username} - ${user.name} (${user.role}) ${marker}`);
      console.log(`   المعرف: ${user.id}`);
      console.log(`   البريد: ${user.email}`);
      console.log(`   الحالة: ${user.status}`);
      console.log('');
    });
    
    if (users.length > 0) {
      const firstUser = users[0];
      console.log('✅ المستخدم الذي سيتم تعيينه كـ currentUser:');
      console.log(`   الاسم: ${firstUser.name}`);
      console.log(`   اسم المستخدم: ${firstUser.username}`);
      console.log(`   الدور: ${firstUser.role}`);
      
      if (firstUser.username !== 'admin') {
        console.log('⚠️ تحذير: المستخدم الأول ليس admin!');
      } else {
        console.log('✅ المستخدم الأول هو admin بشكل صحيح');
      }
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error);
  }
}

testUsersAPI();
